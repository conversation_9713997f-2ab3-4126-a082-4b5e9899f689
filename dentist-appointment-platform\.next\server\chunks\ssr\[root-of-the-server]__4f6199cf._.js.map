{"version": 3, "sources": [], "sections": [{"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/test/dentist-appointment-platform/src/lib/constants.ts"], "sourcesContent": ["// Application Constants\n\n// API Configuration\nexport const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || '/api';\nexport const API_TIMEOUT = 30000; // 30 seconds\n\n// Authentication\nexport const AUTH_TOKEN_KEY = 'auth_token';\nexport const REFRESH_TOKEN_KEY = 'refresh_token';\nexport const SESSION_STORAGE_KEY = 'user_session';\nexport const TOKEN_REFRESH_THRESHOLD = 5 * 60 * 1000; // 5 minutes before expiry\n\n// User Roles\nexport const USER_ROLES = {\n  PATIENT: 'PATIENT',\n  DENTIST: 'DENTIST',\n  ADMIN: 'ADMIN',\n} as const;\n\n// Appointment Configuration\nexport const APPOINTMENT_DURATION_OPTIONS = [\n  { value: 15, label: '15 minutes' },\n  { value: 30, label: '30 minutes' },\n  { value: 45, label: '45 minutes' },\n  { value: 60, label: '1 hour' },\n  { value: 90, label: '1.5 hours' },\n  { value: 120, label: '2 hours' },\n  { value: 180, label: '3 hours' },\n  { value: 240, label: '4 hours' },\n];\n\nexport const APPOINTMENT_TYPES = {\n  CONSULTATION: 'CONSULTATION',\n  CLEANING: 'CLEANING',\n  FILLING: 'FILLING',\n  ROOT_CANAL: 'ROOT_CANAL',\n  EXTRACTION: 'EXTRACTION',\n  CROWN: 'CROWN',\n  BRIDGE: 'BRIDGE',\n  IMPLANT: 'IMPLANT',\n  ORTHODONTICS: 'ORTHODONTICS',\n  EMERGENCY: 'EMERGENCY',\n  FOLLOW_UP: 'FOLLOW_UP',\n  CHECKUP: 'CHECKUP',\n} as const;\n\nexport const APPOINTMENT_STATUS = {\n  SCHEDULED: 'SCHEDULED',\n  CONFIRMED: 'CONFIRMED',\n  IN_PROGRESS: 'IN_PROGRESS',\n  COMPLETED: 'COMPLETED',\n  CANCELLED: 'CANCELLED',\n  NO_SHOW: 'NO_SHOW',\n  RESCHEDULED: 'RESCHEDULED',\n} as const;\n\nexport const APPOINTMENT_PRIORITY = {\n  LOW: 'LOW',\n  NORMAL: 'NORMAL',\n  HIGH: 'HIGH',\n  URGENT: 'URGENT',\n} as const;\n\n// Document Configuration\nexport const DOCUMENT_CATEGORIES = {\n  XRAY: 'XRAY',\n  PHOTO: 'PHOTO',\n  REPORT: 'REPORT',\n  PRESCRIPTION: 'PRESCRIPTION',\n  INSURANCE: 'INSURANCE',\n  CONSENT_FORM: 'CONSENT_FORM',\n  TREATMENT_PLAN: 'TREATMENT_PLAN',\n  INVOICE: 'INVOICE',\n  RECEIPT: 'RECEIPT',\n  LAB_RESULT: 'LAB_RESULT',\n  REFERRAL: 'REFERRAL',\n  OTHER: 'OTHER',\n} as const;\n\nexport const ALLOWED_FILE_TYPES = [\n  'application/pdf',\n  'image/jpeg',\n  'image/jpg',\n  'image/png',\n  'image/webp',\n  'application/dicom',\n  'text/plain',\n  'application/msword',\n  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',\n];\n\nexport const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB\nexport const MAX_FILES_PER_UPLOAD = 5;\n\n// UI Configuration\nexport const SIDEBAR_WIDTH = 280;\nexport const SIDEBAR_COLLAPSED_WIDTH = 80;\nexport const HEADER_HEIGHT = 64;\n\nexport const BREAKPOINTS = {\n  sm: 640,\n  md: 768,\n  lg: 1024,\n  xl: 1280,\n  '2xl': 1536,\n} as const;\n\n// Notification Configuration\nexport const NOTIFICATION_DURATION = {\n  SUCCESS: 5000,\n  ERROR: 0, // Persistent\n  WARNING: 7000,\n  INFO: 5000,\n} as const;\n\n// Date and Time Configuration\nexport const DATE_FORMATS = {\n  SHORT: 'MMM dd, yyyy',\n  LONG: 'MMMM dd, yyyy',\n  WITH_TIME: 'MMM dd, yyyy HH:mm',\n  TIME_ONLY: 'HH:mm',\n  ISO: \"yyyy-MM-dd'T'HH:mm:ss.SSSxxx\",\n} as const;\n\nexport const TIME_ZONES = [\n  { value: 'America/New_York', label: 'Eastern Time (ET)' },\n  { value: 'America/Chicago', label: 'Central Time (CT)' },\n  { value: 'America/Denver', label: 'Mountain Time (MT)' },\n  { value: 'America/Los_Angeles', label: 'Pacific Time (PT)' },\n  { value: 'America/Anchorage', label: 'Alaska Time (AKT)' },\n  { value: 'Pacific/Honolulu', label: 'Hawaii Time (HT)' },\n] as const;\n\n// Working Hours Configuration\nexport const DEFAULT_WORKING_HOURS = {\n  monday: [{ start: '09:00', end: '17:00' }],\n  tuesday: [{ start: '09:00', end: '17:00' }],\n  wednesday: [{ start: '09:00', end: '17:00' }],\n  thursday: [{ start: '09:00', end: '17:00' }],\n  friday: [{ start: '09:00', end: '17:00' }],\n  saturday: [],\n  sunday: [],\n};\n\nexport const TIME_SLOTS = Array.from({ length: 48 }, (_, i) => {\n  const hour = Math.floor(i / 2);\n  const minute = i % 2 === 0 ? '00' : '30';\n  const time = `${hour.toString().padStart(2, '0')}:${minute}`;\n  const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;\n  const ampm = hour < 12 ? 'AM' : 'PM';\n  const display = `${displayHour}:${minute} ${ampm}`;\n  return { value: time, label: display };\n});\n\n// Validation Rules\nexport const VALIDATION_RULES = {\n  EMAIL: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\n  PHONE: /^\\+?[\\d\\s\\-\\(\\)]+$/,\n  PASSWORD_MIN_LENGTH: 8,\n  NAME_MIN_LENGTH: 2,\n  NAME_MAX_LENGTH: 50,\n  DESCRIPTION_MAX_LENGTH: 1000,\n  NOTES_MAX_LENGTH: 2000,\n} as const;\n\n// Error Messages\nexport const ERROR_MESSAGES = {\n  REQUIRED: 'This field is required',\n  INVALID_EMAIL: 'Please enter a valid email address',\n  INVALID_PHONE: 'Please enter a valid phone number',\n  PASSWORD_TOO_SHORT: `Password must be at least ${VALIDATION_RULES.PASSWORD_MIN_LENGTH} characters`,\n  NAME_TOO_SHORT: `Name must be at least ${VALIDATION_RULES.NAME_MIN_LENGTH} characters`,\n  NAME_TOO_LONG: `Name must be less than ${VALIDATION_RULES.NAME_MAX_LENGTH} characters`,\n  DESCRIPTION_TOO_LONG: `Description must be less than ${VALIDATION_RULES.DESCRIPTION_MAX_LENGTH} characters`,\n  NOTES_TOO_LONG: `Notes must be less than ${VALIDATION_RULES.NOTES_MAX_LENGTH} characters`,\n  FILE_TOO_LARGE: `File size must be less than ${MAX_FILE_SIZE / (1024 * 1024)}MB`,\n  INVALID_FILE_TYPE: 'File type not supported',\n  TOO_MANY_FILES: `Maximum ${MAX_FILES_PER_UPLOAD} files allowed`,\n  NETWORK_ERROR: 'Network error. Please check your connection and try again.',\n  UNAUTHORIZED: 'You are not authorized to perform this action',\n  FORBIDDEN: 'Access denied',\n  NOT_FOUND: 'The requested resource was not found',\n  SERVER_ERROR: 'Server error. Please try again later.',\n  VALIDATION_ERROR: 'Please check your input and try again',\n} as const;\n\n// Success Messages\nexport const SUCCESS_MESSAGES = {\n  APPOINTMENT_CREATED: 'Appointment created successfully',\n  APPOINTMENT_UPDATED: 'Appointment updated successfully',\n  APPOINTMENT_CANCELLED: 'Appointment cancelled successfully',\n  PROFILE_UPDATED: 'Profile updated successfully',\n  DOCUMENT_UPLOADED: 'Document uploaded successfully',\n  DOCUMENT_DELETED: 'Document deleted successfully',\n  PASSWORD_CHANGED: 'Password changed successfully',\n  EMAIL_SENT: 'Email sent successfully',\n  SETTINGS_SAVED: 'Settings saved successfully',\n} as const;\n\n// Feature Flags\nexport const FEATURE_FLAGS = {\n  ENABLE_NOTIFICATIONS: true,\n  ENABLE_DARK_MODE: true,\n  ENABLE_ANALYTICS: false,\n  ENABLE_CHAT: false,\n  ENABLE_VIDEO_CALLS: false,\n  ENABLE_PAYMENT_PROCESSING: false,\n  ENABLE_INSURANCE_INTEGRATION: false,\n} as const;\n\n// Environment Configuration\nexport const IS_DEVELOPMENT = process.env.NODE_ENV === 'development';\nexport const IS_PRODUCTION = process.env.NODE_ENV === 'production';\nexport const IS_TEST = process.env.NODE_ENV === 'test';\n\n// External Service URLs\nexport const EXTERNAL_URLS = {\n  PRIVACY_POLICY: '/privacy',\n  TERMS_OF_SERVICE: '/terms',\n  SUPPORT: '/support',\n  DOCUMENTATION: '/docs',\n  STATUS_PAGE: 'https://status.example.com',\n} as const;\n"], "names": [], "mappings": "AAAA,wBAAwB;AAExB,oBAAoB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACb,MAAM,eAAe,QAAQ,GAAG,CAAC,mBAAmB,IAAI;AACxD,MAAM,cAAc,OAAO,aAAa;AAGxC,MAAM,iBAAiB;AACvB,MAAM,oBAAoB;AAC1B,MAAM,sBAAsB;AAC5B,MAAM,0BAA0B,IAAI,KAAK,MAAM,0BAA0B;AAGzE,MAAM,aAAa;IACxB,SAAS;IACT,SAAS;IACT,OAAO;AACT;AAGO,MAAM,+BAA+B;IAC1C;QAAE,OAAO;QAAI,OAAO;IAAa;IACjC;QAAE,OAAO;QAAI,OAAO;IAAa;IACjC;QAAE,OAAO;QAAI,OAAO;IAAa;IACjC;QAAE,OAAO;QAAI,OAAO;IAAS;IAC7B;QAAE,OAAO;QAAI,OAAO;IAAY;IAChC;QAAE,OAAO;QAAK,OAAO;IAAU;IAC/B;QAAE,OAAO;QAAK,OAAO;IAAU;IAC/B;QAAE,OAAO;QAAK,OAAO;IAAU;CAChC;AAEM,MAAM,oBAAoB;IAC/B,cAAc;IACd,UAAU;IACV,SAAS;IACT,YAAY;IACZ,YAAY;IACZ,OAAO;IACP,QAAQ;IACR,SAAS;IACT,cAAc;IACd,WAAW;IACX,WAAW;IACX,SAAS;AACX;AAEO,MAAM,qBAAqB;IAChC,WAAW;IACX,WAAW;IACX,aAAa;IACb,WAAW;IACX,WAAW;IACX,SAAS;IACT,aAAa;AACf;AAEO,MAAM,uBAAuB;IAClC,KAAK;IACL,QAAQ;IACR,MAAM;IACN,QAAQ;AACV;AAGO,MAAM,sBAAsB;IACjC,MAAM;IACN,OAAO;IACP,QAAQ;IACR,cAAc;IACd,WAAW;IACX,cAAc;IACd,gBAAgB;IAChB,SAAS;IACT,SAAS;IACT,YAAY;IACZ,UAAU;IACV,OAAO;AACT;AAEO,MAAM,qBAAqB;IAChC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAEM,MAAM,gBAAgB,KAAK,OAAO,MAAM,OAAO;AAC/C,MAAM,uBAAuB;AAG7B,MAAM,gBAAgB;AACtB,MAAM,0BAA0B;AAChC,MAAM,gBAAgB;AAEtB,MAAM,cAAc;IACzB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,OAAO;AACT;AAGO,MAAM,wBAAwB;IACnC,SAAS;IACT,OAAO;IACP,SAAS;IACT,MAAM;AACR;AAGO,MAAM,eAAe;IAC1B,OAAO;IACP,MAAM;IACN,WAAW;IACX,WAAW;IACX,KAAK;AACP;AAEO,MAAM,aAAa;IACxB;QAAE,OAAO;QAAoB,OAAO;IAAoB;IACxD;QAAE,OAAO;QAAmB,OAAO;IAAoB;IACvD;QAAE,OAAO;QAAkB,OAAO;IAAqB;IACvD;QAAE,OAAO;QAAuB,OAAO;IAAoB;IAC3D;QAAE,OAAO;QAAqB,OAAO;IAAoB;IACzD;QAAE,OAAO;QAAoB,OAAO;IAAmB;CACxD;AAGM,MAAM,wBAAwB;IACnC,QAAQ;QAAC;YAAE,OAAO;YAAS,KAAK;QAAQ;KAAE;IAC1C,SAAS;QAAC;YAAE,OAAO;YAAS,KAAK;QAAQ;KAAE;IAC3C,WAAW;QAAC;YAAE,OAAO;YAAS,KAAK;QAAQ;KAAE;IAC7C,UAAU;QAAC;YAAE,OAAO;YAAS,KAAK;QAAQ;KAAE;IAC5C,QAAQ;QAAC;YAAE,OAAO;YAAS,KAAK;QAAQ;KAAE;IAC1C,UAAU,EAAE;IACZ,QAAQ,EAAE;AACZ;AAEO,MAAM,aAAa,MAAM,IAAI,CAAC;IAAE,QAAQ;AAAG,GAAG,CAAC,GAAG;IACvD,MAAM,OAAO,KAAK,KAAK,CAAC,IAAI;IAC5B,MAAM,SAAS,IAAI,MAAM,IAAI,OAAO;IACpC,MAAM,OAAO,GAAG,KAAK,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,QAAQ;IAC5D,MAAM,cAAc,SAAS,IAAI,KAAK,OAAO,KAAK,OAAO,KAAK;IAC9D,MAAM,OAAO,OAAO,KAAK,OAAO;IAChC,MAAM,UAAU,GAAG,YAAY,CAAC,EAAE,OAAO,CAAC,EAAE,MAAM;IAClD,OAAO;QAAE,OAAO;QAAM,OAAO;IAAQ;AACvC;AAGO,MAAM,mBAAmB;IAC9B,OAAO;IACP,OAAO;IACP,qBAAqB;IACrB,iBAAiB;IACjB,iBAAiB;IACjB,wBAAwB;IACxB,kBAAkB;AACpB;AAGO,MAAM,iBAAiB;IAC5B,UAAU;IACV,eAAe;IACf,eAAe;IACf,oBAAoB,CAAC,0BAA0B,EAAE,iBAAiB,mBAAmB,CAAC,WAAW,CAAC;IAClG,gBAAgB,CAAC,sBAAsB,EAAE,iBAAiB,eAAe,CAAC,WAAW,CAAC;IACtF,eAAe,CAAC,uBAAuB,EAAE,iBAAiB,eAAe,CAAC,WAAW,CAAC;IACtF,sBAAsB,CAAC,8BAA8B,EAAE,iBAAiB,sBAAsB,CAAC,WAAW,CAAC;IAC3G,gBAAgB,CAAC,wBAAwB,EAAE,iBAAiB,gBAAgB,CAAC,WAAW,CAAC;IACzF,gBAAgB,CAAC,4BAA4B,EAAE,gBAAgB,CAAC,OAAO,IAAI,EAAE,EAAE,CAAC;IAChF,mBAAmB;IACnB,gBAAgB,CAAC,QAAQ,EAAE,qBAAqB,cAAc,CAAC;IAC/D,eAAe;IACf,cAAc;IACd,WAAW;IACX,WAAW;IACX,cAAc;IACd,kBAAkB;AACpB;AAGO,MAAM,mBAAmB;IAC9B,qBAAqB;IACrB,qBAAqB;IACrB,uBAAuB;IACvB,iBAAiB;IACjB,mBAAmB;IACnB,kBAAkB;IAClB,kBAAkB;IAClB,YAAY;IACZ,gBAAgB;AAClB;AAGO,MAAM,gBAAgB;IAC3B,sBAAsB;IACtB,kBAAkB;IAClB,kBAAkB;IAClB,aAAa;IACb,oBAAoB;IACpB,2BAA2B;IAC3B,8BAA8B;AAChC;AAGO,MAAM,iBAAiB,oDAAyB;AAChD,MAAM,gBAAgB,oDAAyB;AAC/C,MAAM,UAAU,oDAAyB;AAGzC,MAAM,gBAAgB;IAC3B,gBAAgB;IAChB,kBAAkB;IAClB,SAAS;IACT,eAAe;IACf,aAAa;AACf", "debugId": null}}, {"offset": {"line": 337, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/test/dentist-appointment-platform/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n// import { format, parseISO, isValid, differenceInMinutes, addMinutes, startOfDay, endOfDay } from 'date-fns';\nimport { VALIDATION_RULES, ERROR_MESSAGES } from './constants';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\n// Date and Time Utilities (temporarily commented out due to date-fns issues)\n// TODO: Re-enable when date-fns is properly configured\nexport const dateUtils = {\n  format: (date: Date | string, formatStr: string = 'MMM dd, yyyy') => {\n    try {\n      const dateObj = typeof date === 'string' ? new Date(date) : date;\n      return dateObj.toLocaleDateString();\n    } catch {\n      return 'Invalid Date';\n    }\n  },\n\n  formatTime: (time: string) => {\n    try {\n      const [hours, minutes] = time.split(':').map(Number);\n      const date = new Date();\n      date.setHours(hours, minutes, 0, 0);\n      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });\n    } catch {\n      return time;\n    }\n  },\n\n  formatDateTime: (date: Date | string) => {\n    try {\n      const dateObj = typeof date === 'string' ? new Date(date) : date;\n      return dateObj.toLocaleString();\n    } catch {\n      return 'Invalid Date';\n    }\n  },\n\n  isToday: (date: Date | string) => {\n    try {\n      const dateObj = typeof date === 'string' ? new Date(date) : date;\n      const today = new Date();\n      return dateObj.toDateString() === today.toDateString();\n    } catch {\n      return false;\n    }\n  },\n\n  isFuture: (date: Date | string) => {\n    try {\n      const dateObj = typeof date === 'string' ? new Date(date) : date;\n      return dateObj > new Date();\n    } catch {\n      return false;\n    }\n  },\n\n  isPast: (date: Date | string) => {\n    try {\n      const dateObj = typeof date === 'string' ? new Date(date) : date;\n      return dateObj < new Date();\n    } catch {\n      return false;\n    }\n  },\n\n  getDuration: (startTime: string, endTime: string) => {\n    try {\n      const start = new Date(`2000-01-01T${startTime}`);\n      const end = new Date(`2000-01-01T${endTime}`);\n      return Math.floor((end.getTime() - start.getTime()) / (1000 * 60));\n    } catch {\n      return 0;\n    }\n  },\n\n  addMinutesToTime: (time: string, minutes: number) => {\n    try {\n      const [hours, mins] = time.split(':').map(Number);\n      const date = new Date();\n      date.setHours(hours, mins, 0, 0);\n      date.setMinutes(date.getMinutes() + minutes);\n      return date.toTimeString().slice(0, 5);\n    } catch {\n      return time;\n    }\n  },\n\n  getStartOfDay: (date: Date | string) => {\n    const dateObj = typeof date === 'string' ? new Date(date) : date;\n    const start = new Date(dateObj);\n    start.setHours(0, 0, 0, 0);\n    return start;\n  },\n\n  getEndOfDay: (date: Date | string) => {\n    const dateObj = typeof date === 'string' ? new Date(date) : date;\n    const end = new Date(dateObj);\n    end.setHours(23, 59, 59, 999);\n    return end;\n  },\n};\n\n// String Utilities\nexport const stringUtils = {\n  capitalize: (str: string) => {\n    return str.charAt(0).toUpperCase() + str.slice(1).toLowerCase();\n  },\n\n  capitalizeWords: (str: string) => {\n    return str.replace(/\\w\\S*/g, (txt) =>\n      txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()\n    );\n  },\n\n  truncate: (str: string, length: number, suffix: string = '...') => {\n    if (str.length <= length) return str;\n    return str.substring(0, length) + suffix;\n  },\n\n  slugify: (str: string) => {\n    return str\n      .toLowerCase()\n      .replace(/[^\\w\\s-]/g, '')\n      .replace(/[\\s_-]+/g, '-')\n      .replace(/^-+|-+$/g, '');\n  },\n\n  initials: (name: string) => {\n    return name\n      .split(' ')\n      .map(word => word.charAt(0))\n      .join('')\n      .toUpperCase()\n      .substring(0, 2);\n  },\n\n  formatPhoneNumber: (phone: string) => {\n    const cleaned = phone.replace(/\\D/g, '');\n    if (cleaned.length === 10) {\n      return `(${cleaned.slice(0, 3)}) ${cleaned.slice(3, 6)}-${cleaned.slice(6)}`;\n    }\n    return phone;\n  },\n\n  maskEmail: (email: string) => {\n    const [username, domain] = email.split('@');\n    if (username.length <= 2) return email;\n    const maskedUsername = username.charAt(0) + '*'.repeat(username.length - 2) + username.charAt(username.length - 1);\n    return `${maskedUsername}@${domain}`;\n  },\n};\n\n// Number Utilities\nexport const numberUtils = {\n  formatCurrency: (amount: number, currency: string = 'USD') => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency,\n    }).format(amount);\n  },\n\n  formatNumber: (num: number, decimals: number = 0) => {\n    return new Intl.NumberFormat('en-US', {\n      minimumFractionDigits: decimals,\n      maximumFractionDigits: decimals,\n    }).format(num);\n  },\n\n  formatFileSize: (bytes: number) => {\n    if (bytes === 0) return '0 Bytes';\n    const k = 1024;\n    const sizes = ['Bytes', 'KB', 'MB', 'GB'];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];\n  },\n\n  clamp: (num: number, min: number, max: number) => {\n    return Math.min(Math.max(num, min), max);\n  },\n\n  randomBetween: (min: number, max: number) => {\n    return Math.floor(Math.random() * (max - min + 1)) + min;\n  },\n};\n\n// Array Utilities\nexport const arrayUtils = {\n  unique: <T>(array: T[]): T[] => {\n    return [...new Set(array)];\n  },\n\n  groupBy: <T, K extends keyof T>(array: T[], key: K): Record<string, T[]> => {\n    return array.reduce((groups, item) => {\n      const group = String(item[key]);\n      groups[group] = groups[group] || [];\n      groups[group].push(item);\n      return groups;\n    }, {} as Record<string, T[]>);\n  },\n\n  sortBy: <T>(array: T[], key: keyof T, direction: 'asc' | 'desc' = 'asc'): T[] => {\n    return [...array].sort((a, b) => {\n      const aVal = a[key];\n      const bVal = b[key];\n\n      if (aVal < bVal) return direction === 'asc' ? -1 : 1;\n      if (aVal > bVal) return direction === 'asc' ? 1 : -1;\n      return 0;\n    });\n  },\n\n  chunk: <T>(array: T[], size: number): T[][] => {\n    const chunks: T[][] = [];\n    for (let i = 0; i < array.length; i += size) {\n      chunks.push(array.slice(i, i + size));\n    }\n    return chunks;\n  },\n\n  shuffle: <T>(array: T[]): T[] => {\n    const shuffled = [...array];\n    for (let i = shuffled.length - 1; i > 0; i--) {\n      const j = Math.floor(Math.random() * (i + 1));\n      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];\n    }\n    return shuffled;\n  },\n};\n\n// Validation Utilities\nexport const validationUtils = {\n  isEmail: (email: string): boolean => {\n    return VALIDATION_RULES.EMAIL.test(email);\n  },\n\n  isPhone: (phone: string): boolean => {\n    return VALIDATION_RULES.PHONE.test(phone);\n  },\n\n  isStrongPassword: (password: string): boolean => {\n    return (\n      password.length >= VALIDATION_RULES.PASSWORD_MIN_LENGTH &&\n      /[A-Z]/.test(password) &&\n      /[a-z]/.test(password) &&\n      /\\d/.test(password) &&\n      /[!@#$%^&*(),.?\":{}|<>]/.test(password)\n    );\n  },\n\n  validateRequired: (value: any): string | null => {\n    if (value === null || value === undefined || value === '') {\n      return ERROR_MESSAGES.REQUIRED;\n    }\n    return null;\n  },\n\n  validateEmail: (email: string): string | null => {\n    if (!email) return ERROR_MESSAGES.REQUIRED;\n    if (!validationUtils.isEmail(email)) return ERROR_MESSAGES.INVALID_EMAIL;\n    return null;\n  },\n\n  validatePhone: (phone: string): string | null => {\n    if (!phone) return ERROR_MESSAGES.REQUIRED;\n    if (!validationUtils.isPhone(phone)) return ERROR_MESSAGES.INVALID_PHONE;\n    return null;\n  },\n\n  validatePassword: (password: string): string | null => {\n    if (!password) return ERROR_MESSAGES.REQUIRED;\n    if (password.length < VALIDATION_RULES.PASSWORD_MIN_LENGTH) {\n      return ERROR_MESSAGES.PASSWORD_TOO_SHORT;\n    }\n    return null;\n  },\n};\n\n// Debounce and Throttle Utilities\nexport const debounce = <T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): ((...args: Parameters<T>) => void) => {\n  let timeout: NodeJS.Timeout;\n\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n};\n\nexport const throttle = <T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): ((...args: Parameters<T>) => void) => {\n  let inThrottle: boolean;\n\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => (inThrottle = false), limit);\n    }\n  };\n};\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AACA;AACA,+GAA+G;AAC/G;;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAIO,MAAM,YAAY;IACvB,QAAQ,CAAC,MAAqB,YAAoB,cAAc;QAC9D,IAAI;YACF,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;YAC5D,OAAO,QAAQ,kBAAkB;QACnC,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA,YAAY,CAAC;QACX,IAAI;YACF,MAAM,CAAC,OAAO,QAAQ,GAAG,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC;YAC7C,MAAM,OAAO,IAAI;YACjB,KAAK,QAAQ,CAAC,OAAO,SAAS,GAAG;YACjC,OAAO,KAAK,kBAAkB,CAAC,EAAE,EAAE;gBAAE,MAAM;gBAAW,QAAQ;YAAU;QAC1E,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA,gBAAgB,CAAC;QACf,IAAI;YACF,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;YAC5D,OAAO,QAAQ,cAAc;QAC/B,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA,SAAS,CAAC;QACR,IAAI;YACF,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;YAC5D,MAAM,QAAQ,IAAI;YAClB,OAAO,QAAQ,YAAY,OAAO,MAAM,YAAY;QACtD,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA,UAAU,CAAC;QACT,IAAI;YACF,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;YAC5D,OAAO,UAAU,IAAI;QACvB,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA,QAAQ,CAAC;QACP,IAAI;YACF,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;YAC5D,OAAO,UAAU,IAAI;QACvB,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA,aAAa,CAAC,WAAmB;QAC/B,IAAI;YACF,MAAM,QAAQ,IAAI,KAAK,CAAC,WAAW,EAAE,WAAW;YAChD,MAAM,MAAM,IAAI,KAAK,CAAC,WAAW,EAAE,SAAS;YAC5C,OAAO,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,MAAM,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE;QAClE,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA,kBAAkB,CAAC,MAAc;QAC/B,IAAI;YACF,MAAM,CAAC,OAAO,KAAK,GAAG,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC;YAC1C,MAAM,OAAO,IAAI;YACjB,KAAK,QAAQ,CAAC,OAAO,MAAM,GAAG;YAC9B,KAAK,UAAU,CAAC,KAAK,UAAU,KAAK;YACpC,OAAO,KAAK,YAAY,GAAG,KAAK,CAAC,GAAG;QACtC,EAAE,OAAM;YACN,OAAO;QACT;IACF;IAEA,eAAe,CAAC;QACd,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;QAC5D,MAAM,QAAQ,IAAI,KAAK;QACvB,MAAM,QAAQ,CAAC,GAAG,GAAG,GAAG;QACxB,OAAO;IACT;IAEA,aAAa,CAAC;QACZ,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;QAC5D,MAAM,MAAM,IAAI,KAAK;QACrB,IAAI,QAAQ,CAAC,IAAI,IAAI,IAAI;QACzB,OAAO;IACT;AACF;AAGO,MAAM,cAAc;IACzB,YAAY,CAAC;QACX,OAAO,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,KAAK,CAAC,GAAG,WAAW;IAC/D;IAEA,iBAAiB,CAAC;QAChB,OAAO,IAAI,OAAO,CAAC,UAAU,CAAC,MAC5B,IAAI,MAAM,CAAC,GAAG,WAAW,KAAK,IAAI,MAAM,CAAC,GAAG,WAAW;IAE3D;IAEA,UAAU,CAAC,KAAa,QAAgB,SAAiB,KAAK;QAC5D,IAAI,IAAI,MAAM,IAAI,QAAQ,OAAO;QACjC,OAAO,IAAI,SAAS,CAAC,GAAG,UAAU;IACpC;IAEA,SAAS,CAAC;QACR,OAAO,IACJ,WAAW,GACX,OAAO,CAAC,aAAa,IACrB,OAAO,CAAC,YAAY,KACpB,OAAO,CAAC,YAAY;IACzB;IAEA,UAAU,CAAC;QACT,OAAO,KACJ,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,IACxB,IAAI,CAAC,IACL,WAAW,GACX,SAAS,CAAC,GAAG;IAClB;IAEA,mBAAmB,CAAC;QAClB,MAAM,UAAU,MAAM,OAAO,CAAC,OAAO;QACrC,IAAI,QAAQ,MAAM,KAAK,IAAI;YACzB,OAAO,CAAC,CAAC,EAAE,QAAQ,KAAK,CAAC,GAAG,GAAG,EAAE,EAAE,QAAQ,KAAK,CAAC,GAAG,GAAG,CAAC,EAAE,QAAQ,KAAK,CAAC,IAAI;QAC9E;QACA,OAAO;IACT;IAEA,WAAW,CAAC;QACV,MAAM,CAAC,UAAU,OAAO,GAAG,MAAM,KAAK,CAAC;QACvC,IAAI,SAAS,MAAM,IAAI,GAAG,OAAO;QACjC,MAAM,iBAAiB,SAAS,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,SAAS,MAAM,GAAG,KAAK,SAAS,MAAM,CAAC,SAAS,MAAM,GAAG;QAChH,OAAO,GAAG,eAAe,CAAC,EAAE,QAAQ;IACtC;AACF;AAGO,MAAM,cAAc;IACzB,gBAAgB,CAAC,QAAgB,WAAmB,KAAK;QACvD,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP;QACF,GAAG,MAAM,CAAC;IACZ;IAEA,cAAc,CAAC,KAAa,WAAmB,CAAC;QAC9C,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,uBAAuB;YACvB,uBAAuB;QACzB,GAAG,MAAM,CAAC;IACZ;IAEA,gBAAgB,CAAC;QACf,IAAI,UAAU,GAAG,OAAO;QACxB,MAAM,IAAI;QACV,MAAM,QAAQ;YAAC;YAAS;YAAM;YAAM;SAAK;QACzC,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,CAAC;QAChD,OAAO,WAAW,CAAC,QAAQ,KAAK,GAAG,CAAC,GAAG,EAAE,EAAE,OAAO,CAAC,MAAM,MAAM,KAAK,CAAC,EAAE;IACzE;IAEA,OAAO,CAAC,KAAa,KAAa;QAChC,OAAO,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,KAAK,MAAM;IACtC;IAEA,eAAe,CAAC,KAAa;QAC3B,OAAO,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,MAAM,MAAM,CAAC,KAAK;IACvD;AACF;AAGO,MAAM,aAAa;IACxB,QAAQ,CAAI;QACV,OAAO;eAAI,IAAI,IAAI;SAAO;IAC5B;IAEA,SAAS,CAAuB,OAAY;QAC1C,OAAO,MAAM,MAAM,CAAC,CAAC,QAAQ;YAC3B,MAAM,QAAQ,OAAO,IAAI,CAAC,IAAI;YAC9B,MAAM,CAAC,MAAM,GAAG,MAAM,CAAC,MAAM,IAAI,EAAE;YACnC,MAAM,CAAC,MAAM,CAAC,IAAI,CAAC;YACnB,OAAO;QACT,GAAG,CAAC;IACN;IAEA,QAAQ,CAAI,OAAY,KAAc,YAA4B,KAAK;QACrE,OAAO;eAAI;SAAM,CAAC,IAAI,CAAC,CAAC,GAAG;YACzB,MAAM,OAAO,CAAC,CAAC,IAAI;YACnB,MAAM,OAAO,CAAC,CAAC,IAAI;YAEnB,IAAI,OAAO,MAAM,OAAO,cAAc,QAAQ,CAAC,IAAI;YACnD,IAAI,OAAO,MAAM,OAAO,cAAc,QAAQ,IAAI,CAAC;YACnD,OAAO;QACT;IACF;IAEA,OAAO,CAAI,OAAY;QACrB,MAAM,SAAgB,EAAE;QACxB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,KAAK,KAAM;YAC3C,OAAO,IAAI,CAAC,MAAM,KAAK,CAAC,GAAG,IAAI;QACjC;QACA,OAAO;IACT;IAEA,SAAS,CAAI;QACX,MAAM,WAAW;eAAI;SAAM;QAC3B,IAAK,IAAI,IAAI,SAAS,MAAM,GAAG,GAAG,IAAI,GAAG,IAAK;YAC5C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,IAAI,CAAC;YAC3C,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,EAAE,CAAC,GAAG;gBAAC,QAAQ,CAAC,EAAE;gBAAE,QAAQ,CAAC,EAAE;aAAC;QACzD;QACA,OAAO;IACT;AACF;AAGO,MAAM,kBAAkB;IAC7B,SAAS,CAAC;QACR,OAAO,uHAAA,CAAA,mBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC;IACrC;IAEA,SAAS,CAAC;QACR,OAAO,uHAAA,CAAA,mBAAgB,CAAC,KAAK,CAAC,IAAI,CAAC;IACrC;IAEA,kBAAkB,CAAC;QACjB,OACE,SAAS,MAAM,IAAI,uHAAA,CAAA,mBAAgB,CAAC,mBAAmB,IACvD,QAAQ,IAAI,CAAC,aACb,QAAQ,IAAI,CAAC,aACb,KAAK,IAAI,CAAC,aACV,yBAAyB,IAAI,CAAC;IAElC;IAEA,kBAAkB,CAAC;QACjB,IAAI,UAAU,QAAQ,UAAU,aAAa,UAAU,IAAI;YACzD,OAAO,uHAAA,CAAA,iBAAc,CAAC,QAAQ;QAChC;QACA,OAAO;IACT;IAEA,eAAe,CAAC;QACd,IAAI,CAAC,OAAO,OAAO,uHAAA,CAAA,iBAAc,CAAC,QAAQ;QAC1C,IAAI,CAAC,gBAAgB,OAAO,CAAC,QAAQ,OAAO,uHAAA,CAAA,iBAAc,CAAC,aAAa;QACxE,OAAO;IACT;IAEA,eAAe,CAAC;QACd,IAAI,CAAC,OAAO,OAAO,uHAAA,CAAA,iBAAc,CAAC,QAAQ;QAC1C,IAAI,CAAC,gBAAgB,OAAO,CAAC,QAAQ,OAAO,uHAAA,CAAA,iBAAc,CAAC,aAAa;QACxE,OAAO;IACT;IAEA,kBAAkB,CAAC;QACjB,IAAI,CAAC,UAAU,OAAO,uHAAA,CAAA,iBAAc,CAAC,QAAQ;QAC7C,IAAI,SAAS,MAAM,GAAG,uHAAA,CAAA,mBAAgB,CAAC,mBAAmB,EAAE;YAC1D,OAAO,uHAAA,CAAA,iBAAc,CAAC,kBAAkB;QAC1C;QACA,OAAO;IACT;AACF;AAGO,MAAM,WAAW,CACtB,MACA;IAEA,IAAI;IAEJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,MAAM,WAAW,CACtB,MACA;IAEA,IAAI;IAEJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF", "debugId": null}}, {"offset": {"line": 611, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/test/dentist-appointment-platform/src/types/auth.ts"], "sourcesContent": ["// Authentication and User Types\nexport enum UserRole {\n  PATIENT = 'PATIENT',\n  DENTIST = 'DENTIST',\n  ADMIN = 'ADMIN'\n}\n\nexport interface User {\n  id: string;\n  email: string;\n  name?: string;\n  image?: string;\n  role: UserRole;\n  createdAt: Date;\n  updatedAt: Date;\n  \n  // Relations\n  patientProfile?: PatientProfile;\n  dentistProfile?: DentistProfile;\n}\n\nexport interface PatientProfile {\n  id: string;\n  userId: string;\n  dateOfBirth?: Date;\n  phone?: string;\n  address?: string;\n  emergencyContact?: string;\n  medicalHistory?: string;\n  allergies?: string[];\n  insuranceInfo?: InsuranceInfo;\n  createdAt: Date;\n  updatedAt: Date;\n  \n  // Relations\n  user: User;\n}\n\nexport interface DentistProfile {\n  id: string;\n  userId: string;\n  licenseNumber: string;\n  specialization: string[];\n  workingHours: WorkingHours;\n  practiceInfo?: PracticeInfo;\n  createdAt: Date;\n  updatedAt: Date;\n  \n  // Relations\n  user: User;\n}\n\nexport interface InsuranceInfo {\n  provider: string;\n  policyNumber: string;\n  groupNumber?: string;\n  expirationDate?: Date;\n}\n\nexport interface WorkingHours {\n  monday?: TimeSlot[];\n  tuesday?: TimeSlot[];\n  wednesday?: TimeSlot[];\n  thursday?: TimeSlot[];\n  friday?: TimeSlot[];\n  saturday?: TimeSlot[];\n  sunday?: TimeSlot[];\n}\n\nexport interface TimeSlot {\n  start: string; // HH:MM format\n  end: string;   // HH:MM format\n}\n\nexport interface PracticeInfo {\n  name: string;\n  address: string;\n  phone: string;\n  email?: string;\n  website?: string;\n}\n\n// Authentication Session Types\nexport interface AuthSession {\n  user: User;\n  accessToken: string;\n  refreshToken?: string;\n  expiresAt: Date;\n}\n\nexport interface LoginCredentials {\n  email: string;\n  password: string;\n}\n\nexport interface RegisterData {\n  email: string;\n  name: string;\n  role: UserRole;\n  password: string;\n  confirmPassword: string;\n}\n\n// Permission Types\nexport type Permission = \n  | 'appointments:read:own'\n  | 'appointments:read:all'\n  | 'appointments:create:own'\n  | 'appointments:create:all'\n  | 'appointments:update:own'\n  | 'appointments:update:all'\n  | 'appointments:delete:own'\n  | 'appointments:delete:all'\n  | 'patients:read:own'\n  | 'patients:read:all'\n  | 'patients:update:own'\n  | 'patients:update:all'\n  | 'documents:read:own'\n  | 'documents:read:all'\n  | 'documents:create:own'\n  | 'documents:create:all'\n  | 'documents:update:own'\n  | 'documents:update:all'\n  | 'documents:delete:own'\n  | 'documents:delete:all'\n  | 'profile:read:own'\n  | 'profile:update:own'\n  | 'practice:manage'\n  | 'users:manage';\n\nexport interface RolePermissions {\n  [UserRole.PATIENT]: Permission[];\n  [UserRole.DENTIST]: Permission[];\n  [UserRole.ADMIN]: Permission[];\n}\n\n// API Response Types\nexport interface AuthResponse {\n  success: boolean;\n  user?: User;\n  token?: string;\n  message?: string;\n  errors?: string[];\n}\n\nexport interface ProfileUpdateData {\n  name?: string;\n  phone?: string;\n  address?: string;\n  emergencyContact?: string;\n  dateOfBirth?: Date;\n  allergies?: string[];\n  medicalHistory?: string;\n  insuranceInfo?: InsuranceInfo;\n}\n"], "names": [], "mappings": "AAAA,gCAAgC;;;;AACzB,IAAA,AAAK,kCAAA;;;;WAAA", "debugId": null}}, {"offset": {"line": 627, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/test/dentist-appointment-platform/src/types/appointment.ts"], "sourcesContent": ["import { User, PatientProfile, DentistProfile } from './auth';\n\n// Appointment Status Enum\nexport enum AppointmentStatus {\n  SCHEDULED = 'SCHEDULED',\n  CONFIRMED = 'CONFIRMED',\n  IN_PROGRESS = 'IN_PROGRESS',\n  COMPLETED = 'COMPLETED',\n  CANCELLED = 'CANCELLED',\n  NO_SHOW = 'NO_SHOW',\n  RESCHEDULED = 'RESCHEDULED'\n}\n\n// Appointment Type Enum\nexport enum AppointmentType {\n  CONSULTATION = 'CONSULTATION',\n  CLEANING = 'CLEANING',\n  FILLING = 'FILLING',\n  ROOT_CANAL = 'ROOT_CANAL',\n  EXTRACTION = 'EXTRACTION',\n  CROWN = 'CROWN',\n  BRIDGE = 'BRIDGE',\n  IMPLANT = 'IMPLANT',\n  ORTHODONTICS = 'ORTHODONTICS',\n  EMERGENCY = 'EMERGENCY',\n  FOLLOW_UP = 'FOLLOW_UP',\n  CHECKUP = 'CHECKUP'\n}\n\n// Priority Level\nexport enum AppointmentPriority {\n  LOW = 'LOW',\n  NORMAL = 'NORMAL',\n  HIGH = 'HIGH',\n  URGENT = 'URGENT'\n}\n\n// Main Appointment Interface\nexport interface Appointment {\n  id: string;\n  patientId: string;\n  dentistId: string;\n  appointmentDate: Date;\n  startTime: string; // HH:MM format\n  endTime: string;   // HH:MM format\n  duration: number;  // in minutes\n  type: AppointmentType;\n  status: AppointmentStatus;\n  priority: AppointmentPriority;\n  title?: string;\n  description?: string;\n  notes?: string;\n  symptoms?: string[];\n  treatmentPlan?: string;\n  cost?: number;\n  isPaid?: boolean;\n  reminderSent?: boolean;\n  createdAt: Date;\n  updatedAt: Date;\n  \n  // Relations\n  patient: User & { patientProfile: PatientProfile };\n  dentist: User & { dentistProfile: DentistProfile };\n  documents?: AppointmentDocument[];\n  prescriptions?: Prescription[];\n}\n\n// Appointment Document\nexport interface AppointmentDocument {\n  id: string;\n  appointmentId: string;\n  documentId: string;\n  createdAt: Date;\n  \n  // Relations\n  appointment: Appointment;\n  document: Document;\n}\n\n// Prescription\nexport interface Prescription {\n  id: string;\n  appointmentId: string;\n  medication: string;\n  dosage: string;\n  frequency: string;\n  duration: string;\n  instructions?: string;\n  createdAt: Date;\n  updatedAt: Date;\n  \n  // Relations\n  appointment: Appointment;\n}\n\n// Appointment Creation/Update Types\nexport interface CreateAppointmentData {\n  patientId: string;\n  dentistId: string;\n  appointmentDate: Date;\n  startTime: string;\n  duration: number;\n  type: AppointmentType;\n  priority?: AppointmentPriority;\n  title?: string;\n  description?: string;\n  notes?: string;\n  symptoms?: string[];\n}\n\nexport interface UpdateAppointmentData {\n  appointmentDate?: Date;\n  startTime?: string;\n  duration?: number;\n  type?: AppointmentType;\n  status?: AppointmentStatus;\n  priority?: AppointmentPriority;\n  title?: string;\n  description?: string;\n  notes?: string;\n  symptoms?: string[];\n  treatmentPlan?: string;\n  cost?: number;\n  isPaid?: boolean;\n}\n\n// Calendar and Scheduling Types\nexport interface TimeSlot {\n  start: string;\n  end: string;\n  available: boolean;\n  appointmentId?: string;\n}\n\nexport interface DaySchedule {\n  date: Date;\n  timeSlots: TimeSlot[];\n  appointments: Appointment[];\n}\n\nexport interface WeekSchedule {\n  startDate: Date;\n  endDate: Date;\n  days: DaySchedule[];\n}\n\nexport interface AvailabilityQuery {\n  dentistId: string;\n  date: Date;\n  duration: number;\n  type?: AppointmentType;\n}\n\nexport interface AvailableSlot {\n  start: string;\n  end: string;\n  duration: number;\n}\n\n// Appointment Filters and Search\nexport interface AppointmentFilters {\n  status?: AppointmentStatus[];\n  type?: AppointmentType[];\n  priority?: AppointmentPriority[];\n  patientId?: string;\n  dentistId?: string;\n  dateFrom?: Date;\n  dateTo?: Date;\n  search?: string;\n}\n\nexport interface AppointmentSearchParams {\n  query?: string;\n  filters?: AppointmentFilters;\n  sortBy?: 'date' | 'status' | 'type' | 'priority' | 'patient' | 'dentist';\n  sortOrder?: 'asc' | 'desc';\n  page?: number;\n  limit?: number;\n}\n\n// Appointment Statistics\nexport interface AppointmentStats {\n  total: number;\n  scheduled: number;\n  confirmed: number;\n  completed: number;\n  cancelled: number;\n  noShow: number;\n  todayAppointments: number;\n  upcomingAppointments: number;\n  overdueAppointments: number;\n}\n\n// Recurring Appointment Types\nexport enum RecurrenceType {\n  NONE = 'NONE',\n  DAILY = 'DAILY',\n  WEEKLY = 'WEEKLY',\n  MONTHLY = 'MONTHLY',\n  YEARLY = 'YEARLY'\n}\n\nexport interface RecurrencePattern {\n  type: RecurrenceType;\n  interval: number; // Every X days/weeks/months/years\n  endDate?: Date;\n  occurrences?: number;\n  daysOfWeek?: number[]; // 0-6, Sunday = 0\n  dayOfMonth?: number;   // 1-31\n}\n\nexport interface RecurringAppointment {\n  id: string;\n  patientId: string;\n  dentistId: string;\n  startDate: Date;\n  startTime: string;\n  duration: number;\n  type: AppointmentType;\n  recurrencePattern: RecurrencePattern;\n  title?: string;\n  description?: string;\n  createdAt: Date;\n  updatedAt: Date;\n  \n  // Generated appointments\n  appointments: Appointment[];\n}\n\n// API Response Types\nexport interface AppointmentResponse {\n  success: boolean;\n  appointment?: Appointment;\n  message?: string;\n  errors?: string[];\n}\n\nexport interface AppointmentsListResponse {\n  success: boolean;\n  appointments: Appointment[];\n  total: number;\n  page: number;\n  limit: number;\n  totalPages: number;\n}\n\nexport interface AvailabilityResponse {\n  success: boolean;\n  availableSlots: AvailableSlot[];\n  date: Date;\n  dentistId: string;\n}\n"], "names": [], "mappings": ";;;;;;AAGO,IAAA,AAAK,2CAAA;;;;;;;;WAAA;;AAWL,IAAA,AAAK,yCAAA;;;;;;;;;;;;;WAAA;;AAgBL,IAAA,AAAK,6CAAA;;;;;WAAA;;AAoKL,IAAA,AAAK,wCAAA;;;;;;WAAA", "debugId": null}}, {"offset": {"line": 679, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/test/dentist-appointment-platform/src/types/document.ts"], "sourcesContent": ["import { User } from './auth';\nimport { Appointment } from './appointment';\n\n// Document Category Enum\nexport enum DocumentCategory {\n  XRAY = 'XRAY',\n  PHOTO = 'PHOTO',\n  REPORT = 'REPORT',\n  PRESCRIPTION = 'PRESCRIPTION',\n  INSURANCE = 'INSURANCE',\n  CONSENT_FORM = 'CONSENT_FORM',\n  TREATMENT_PLAN = 'TREATMENT_PLAN',\n  INVOICE = 'INVOICE',\n  RECEIPT = 'RECEIPT',\n  LAB_RESULT = 'LAB_RESULT',\n  REFERRAL = 'REFERRAL',\n  OTHER = 'OTHER'\n}\n\n// Document Status\nexport enum DocumentStatus {\n  UPLOADED = 'UPLOADED',\n  PROCESSING = 'PROCESSING',\n  APPROVED = 'APPROVED',\n  REJECTED = 'REJECTED',\n  ARCHIVED = 'ARCHIVED'\n}\n\n// Document Access Level\nexport enum DocumentAccessLevel {\n  PRIVATE = 'PRIVATE',     // Only patient and assigned dentist\n  PRACTICE = 'PRACTICE',   // All dentists in practice\n  PUBLIC = 'PUBLIC'        // Patient can share with others\n}\n\n// Main Document Interface\nexport interface Document {\n  id: string;\n  patientId: string;\n  uploadedById: string; // User who uploaded (patient or dentist)\n  appointmentId?: string;\n  filename: string;\n  originalName: string;\n  mimeType: string;\n  size: number; // in bytes\n  category: DocumentCategory;\n  status: DocumentStatus;\n  accessLevel: DocumentAccessLevel;\n  title?: string;\n  description?: string;\n  tags?: string[];\n  metadata?: DocumentMetadata;\n  url: string;\n  thumbnailUrl?: string;\n  downloadCount: number;\n  isEncrypted: boolean;\n  encryptionKey?: string;\n  checksum: string; // For integrity verification\n  virusScanResult?: VirusScanResult;\n  createdAt: Date;\n  updatedAt: Date;\n  expiresAt?: Date;\n  \n  // Relations\n  patient: User;\n  uploadedBy: User;\n  appointment?: Appointment;\n  versions?: DocumentVersion[];\n  shares?: DocumentShare[];\n}\n\n// Document Metadata\nexport interface DocumentMetadata {\n  width?: number;\n  height?: number;\n  pages?: number;\n  duration?: number; // for videos\n  resolution?: string;\n  colorSpace?: string;\n  compression?: string;\n  author?: string;\n  subject?: string;\n  keywords?: string[];\n  createdDate?: Date;\n  modifiedDate?: Date;\n  application?: string;\n  [key: string]: any; // Allow additional metadata\n}\n\n// Virus Scan Result\nexport interface VirusScanResult {\n  isClean: boolean;\n  scanDate: Date;\n  engine: string;\n  version: string;\n  threats?: string[];\n}\n\n// Document Version (for version control)\nexport interface DocumentVersion {\n  id: string;\n  documentId: string;\n  version: number;\n  filename: string;\n  size: number;\n  url: string;\n  checksum: string;\n  uploadedById: string;\n  changeLog?: string;\n  createdAt: Date;\n  \n  // Relations\n  document: Document;\n  uploadedBy: User;\n}\n\n// Document Sharing\nexport interface DocumentShare {\n  id: string;\n  documentId: string;\n  sharedById: string;\n  sharedWithId?: string; // Specific user\n  sharedWithEmail?: string; // External email\n  accessLevel: 'VIEW' | 'DOWNLOAD' | 'EDIT';\n  expiresAt?: Date;\n  password?: string;\n  downloadLimit?: number;\n  downloadCount: number;\n  isActive: boolean;\n  createdAt: Date;\n  lastAccessedAt?: Date;\n  \n  // Relations\n  document: Document;\n  sharedBy: User;\n  sharedWith?: User;\n}\n\n// Document Upload Types\nexport interface DocumentUploadData {\n  file: File;\n  patientId: string;\n  appointmentId?: string;\n  category: DocumentCategory;\n  title?: string;\n  description?: string;\n  tags?: string[];\n  accessLevel?: DocumentAccessLevel;\n}\n\nexport interface DocumentUpdateData {\n  title?: string;\n  description?: string;\n  tags?: string[];\n  category?: DocumentCategory;\n  accessLevel?: DocumentAccessLevel;\n  status?: DocumentStatus;\n}\n\n// Document Search and Filters\nexport interface DocumentFilters {\n  category?: DocumentCategory[];\n  status?: DocumentStatus[];\n  accessLevel?: DocumentAccessLevel[];\n  patientId?: string;\n  appointmentId?: string;\n  uploadedById?: string;\n  dateFrom?: Date;\n  dateTo?: Date;\n  tags?: string[];\n  mimeType?: string[];\n  search?: string;\n}\n\nexport interface DocumentSearchParams {\n  query?: string;\n  filters?: DocumentFilters;\n  sortBy?: 'name' | 'date' | 'size' | 'category' | 'status';\n  sortOrder?: 'asc' | 'desc';\n  page?: number;\n  limit?: number;\n}\n\n// Document Statistics\nexport interface DocumentStats {\n  total: number;\n  byCategory: Record<DocumentCategory, number>;\n  byStatus: Record<DocumentStatus, number>;\n  totalSize: number; // in bytes\n  recentUploads: number;\n  sharedDocuments: number;\n  expiringSoon: number;\n}\n\n// Bulk Operations\nexport interface BulkDocumentOperation {\n  documentIds: string[];\n  operation: 'delete' | 'archive' | 'approve' | 'reject' | 'updateCategory' | 'updateTags';\n  data?: {\n    category?: DocumentCategory;\n    tags?: string[];\n    reason?: string;\n  };\n}\n\n// Document Template (for generating documents)\nexport interface DocumentTemplate {\n  id: string;\n  name: string;\n  category: DocumentCategory;\n  template: string; // HTML template\n  variables: TemplateVariable[];\n  isActive: boolean;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nexport interface TemplateVariable {\n  name: string;\n  type: 'text' | 'number' | 'date' | 'boolean' | 'select';\n  label: string;\n  required: boolean;\n  defaultValue?: any;\n  options?: string[]; // for select type\n  validation?: {\n    min?: number;\n    max?: number;\n    pattern?: string;\n  };\n}\n\n// Document Generation\nexport interface GenerateDocumentData {\n  templateId: string;\n  patientId: string;\n  appointmentId?: string;\n  variables: Record<string, any>;\n  title?: string;\n  description?: string;\n}\n\n// API Response Types\nexport interface DocumentResponse {\n  success: boolean;\n  document?: Document;\n  message?: string;\n  errors?: string[];\n}\n\nexport interface DocumentsListResponse {\n  success: boolean;\n  documents: Document[];\n  total: number;\n  page: number;\n  limit: number;\n  totalPages: number;\n}\n\nexport interface DocumentUploadResponse {\n  success: boolean;\n  document?: Document;\n  uploadProgress?: number;\n  message?: string;\n  errors?: string[];\n}\n\nexport interface DocumentShareResponse {\n  success: boolean;\n  share?: DocumentShare;\n  shareUrl?: string;\n  message?: string;\n  errors?: string[];\n}\n\n// File Upload Progress\nexport interface UploadProgress {\n  documentId?: string;\n  filename: string;\n  progress: number; // 0-100\n  status: 'uploading' | 'processing' | 'completed' | 'error';\n  error?: string;\n  estimatedTimeRemaining?: number; // in seconds\n}\n"], "names": [], "mappings": ";;;;;AAIO,IAAA,AAAK,0CAAA;;;;;;;;;;;;;WAAA;;AAgBL,IAAA,AAAK,wCAAA;;;;;;WAAA;;AASL,IAAA,AAAK,6CAAA;;;8CAGe,gCAAgC;WAH/C", "debugId": null}}, {"offset": {"line": 719, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/test/dentist-appointment-platform/src/types/common.ts"], "sourcesContent": ["// Common utility types and interfaces\n\n// API Response Types\nexport interface ApiResponse<T = any> {\n  success: boolean;\n  data?: T;\n  message?: string;\n  errors?: string[];\n  meta?: ResponseMeta;\n}\n\nexport interface ResponseMeta {\n  page?: number;\n  limit?: number;\n  total?: number;\n  totalPages?: number;\n  hasNextPage?: boolean;\n  hasPrevPage?: boolean;\n}\n\n// Pagination Types\nexport interface PaginationParams {\n  page?: number;\n  limit?: number;\n  offset?: number;\n}\n\nexport interface PaginatedResponse<T> {\n  items: T[];\n  pagination: {\n    page: number;\n    limit: number;\n    total: number;\n    totalPages: number;\n    hasNextPage: boolean;\n    hasPrevPage: boolean;\n  };\n}\n\n// Sort and Filter Types\nexport interface SortParams {\n  sortBy?: string;\n  sortOrder?: 'asc' | 'desc';\n}\n\nexport interface SearchParams extends PaginationParams, SortParams {\n  query?: string;\n  filters?: Record<string, any>;\n}\n\n// Form Types\nexport interface FormField {\n  name: string;\n  label: string;\n  type: 'text' | 'email' | 'password' | 'number' | 'date' | 'select' | 'textarea' | 'checkbox' | 'radio' | 'file';\n  required?: boolean;\n  placeholder?: string;\n  defaultValue?: any;\n  options?: SelectOption[];\n  validation?: ValidationRule[];\n  disabled?: boolean;\n  hidden?: boolean;\n  description?: string;\n}\n\nexport interface SelectOption {\n  value: string | number;\n  label: string;\n  disabled?: boolean;\n  group?: string;\n}\n\nexport interface ValidationRule {\n  type: 'required' | 'email' | 'min' | 'max' | 'pattern' | 'custom';\n  value?: any;\n  message: string;\n}\n\nexport interface FormState {\n  values: Record<string, any>;\n  errors: Record<string, string>;\n  touched: Record<string, boolean>;\n  isSubmitting: boolean;\n  isValid: boolean;\n}\n\n// Loading and Error States\nexport interface LoadingState {\n  isLoading: boolean;\n  loadingMessage?: string;\n}\n\nexport interface ErrorState {\n  hasError: boolean;\n  error?: Error | string;\n  errorCode?: string;\n}\n\nexport interface AsyncState<T = any> extends LoadingState, ErrorState {\n  data?: T;\n  lastUpdated?: Date;\n}\n\n// UI Component Props\nexport interface BaseComponentProps {\n  className?: string;\n  children?: React.ReactNode;\n  id?: string;\n  testId?: string;\n}\n\nexport interface ButtonProps extends BaseComponentProps {\n  variant?: 'default' | 'destructive' | 'outline' | 'secondary' | 'ghost' | 'link' | 'glass';\n  size?: 'default' | 'sm' | 'lg' | 'icon';\n  disabled?: boolean;\n  loading?: boolean;\n  onClick?: () => void;\n  type?: 'button' | 'submit' | 'reset';\n}\n\nexport interface InputProps extends BaseComponentProps {\n  type?: string;\n  placeholder?: string;\n  value?: string;\n  defaultValue?: string;\n  disabled?: boolean;\n  required?: boolean;\n  error?: string;\n  onChange?: (value: string) => void;\n  onBlur?: () => void;\n  onFocus?: () => void;\n}\n\n// Modal and Dialog Types\nexport interface ModalProps extends BaseComponentProps {\n  isOpen: boolean;\n  onClose: () => void;\n  title?: string;\n  description?: string;\n  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';\n  closeOnOverlayClick?: boolean;\n  closeOnEscape?: boolean;\n}\n\nexport interface ConfirmDialogProps extends ModalProps {\n  onConfirm: () => void;\n  confirmText?: string;\n  cancelText?: string;\n  variant?: 'default' | 'destructive';\n}\n\n// Notification Types\nexport enum NotificationType {\n  SUCCESS = 'success',\n  ERROR = 'error',\n  WARNING = 'warning',\n  INFO = 'info'\n}\n\nexport interface Notification {\n  id: string;\n  type: NotificationType;\n  title: string;\n  message?: string;\n  duration?: number; // in milliseconds, 0 for persistent\n  action?: NotificationAction;\n  createdAt: Date;\n}\n\nexport interface NotificationAction {\n  label: string;\n  onClick: () => void;\n}\n\n// Theme and Styling Types\nexport interface Theme {\n  colors: {\n    primary: string;\n    secondary: string;\n    accent: string;\n    background: string;\n    foreground: string;\n    muted: string;\n    border: string;\n    error: string;\n    warning: string;\n    success: string;\n    info: string;\n  };\n  spacing: Record<string, string>;\n  borderRadius: Record<string, string>;\n  fontSize: Record<string, string>;\n  fontWeight: Record<string, string>;\n  shadows: Record<string, string>;\n  transitions: Record<string, string>;\n}\n\n// Date and Time Types\nexport interface DateRange {\n  start: Date;\n  end: Date;\n}\n\nexport interface TimeRange {\n  start: string; // HH:MM format\n  end: string;   // HH:MM format\n}\n\nexport interface DateTimeRange {\n  start: Date;\n  end: Date;\n}\n\n// File and Upload Types\nexport interface FileInfo {\n  name: string;\n  size: number;\n  type: string;\n  lastModified: number;\n}\n\nexport interface UploadConfig {\n  maxSize: number; // in bytes\n  allowedTypes: string[];\n  multiple: boolean;\n  maxFiles?: number;\n}\n\n// Address and Location Types\nexport interface Address {\n  street: string;\n  city: string;\n  state: string;\n  zipCode: string;\n  country: string;\n  coordinates?: {\n    latitude: number;\n    longitude: number;\n  };\n}\n\n// Contact Information\nexport interface ContactInfo {\n  email?: string;\n  phone?: string;\n  mobile?: string;\n  fax?: string;\n  website?: string;\n}\n\n// Audit and Tracking Types\nexport interface AuditLog {\n  id: string;\n  userId: string;\n  action: string;\n  resource: string;\n  resourceId?: string;\n  oldValues?: Record<string, any>;\n  newValues?: Record<string, any>;\n  ipAddress: string;\n  userAgent: string;\n  timestamp: Date;\n  metadata?: Record<string, any>;\n}\n\n// Settings and Configuration Types\nexport interface UserSettings {\n  theme: 'light' | 'dark' | 'system';\n  language: string;\n  timezone: string;\n  dateFormat: string;\n  timeFormat: '12h' | '24h';\n  notifications: NotificationSettings;\n  privacy: PrivacySettings;\n}\n\nexport interface NotificationSettings {\n  email: boolean;\n  sms: boolean;\n  push: boolean;\n  appointmentReminders: boolean;\n  documentSharing: boolean;\n  systemUpdates: boolean;\n}\n\nexport interface PrivacySettings {\n  profileVisibility: 'public' | 'private';\n  shareDataForResearch: boolean;\n  allowMarketing: boolean;\n  twoFactorAuth: boolean;\n}\n\n// Export all types from other files for convenience\nexport * from './auth';\nexport * from './appointment';\nexport * from './document';\n"], "names": [], "mappings": "AAAA,sCAAsC;AAEtC,qBAAqB;;;;AAkSrB,oDAAoD;AACpD;AACA;AACA;AA/IO,IAAA,AAAK,0CAAA;;;;;WAAA", "debugId": null}}, {"offset": {"line": 755, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/test/dentist-appointment-platform/src/store/uiStore.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { persist, createJSONStorage } from 'zustand/middleware';\nimport { Notification, NotificationType } from '@/types/common';\n\ninterface UIState {\n  // Theme and Layout\n  theme: 'light' | 'dark' | 'system';\n  sidebarCollapsed: boolean;\n  sidebarOpen: boolean; // For mobile\n  \n  // Loading states\n  globalLoading: boolean;\n  loadingMessage: string;\n  \n  // Notifications\n  notifications: Notification[];\n  \n  // Modals and Dialogs\n  modals: Record<string, boolean>;\n  \n  // Page state\n  pageTitle: string;\n  breadcrumbs: Breadcrumb[];\n  \n  // Actions\n  setTheme: (theme: 'light' | 'dark' | 'system') => void;\n  toggleSidebar: () => void;\n  setSidebarOpen: (open: boolean) => void;\n  setSidebarCollapsed: (collapsed: boolean) => void;\n  \n  setGlobalLoading: (loading: boolean, message?: string) => void;\n  \n  addNotification: (notification: Omit<Notification, 'id' | 'createdAt'>) => void;\n  removeNotification: (id: string) => void;\n  clearNotifications: () => void;\n  \n  openModal: (modalId: string) => void;\n  closeModal: (modalId: string) => void;\n  toggleModal: (modalId: string) => void;\n  isModalOpen: (modalId: string) => boolean;\n  \n  setPageTitle: (title: string) => void;\n  setBreadcrumbs: (breadcrumbs: Breadcrumb[]) => void;\n  addBreadcrumb: (breadcrumb: Breadcrumb) => void;\n  \n  reset: () => void;\n}\n\ninterface Breadcrumb {\n  label: string;\n  href?: string;\n  icon?: string;\n}\n\n// Helper function to generate notification ID\nconst generateNotificationId = () => {\n  return `notification-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n};\n\nexport const useUIStore = create<UIState>()(\n  persist(\n    (set, get) => ({\n      // Initial state\n      theme: 'dark', // Default to dark theme\n      sidebarCollapsed: false,\n      sidebarOpen: false,\n      globalLoading: false,\n      loadingMessage: '',\n      notifications: [],\n      modals: {},\n      pageTitle: 'Dentist Appointment Platform',\n      breadcrumbs: [],\n\n      // Theme and Layout Actions\n      setTheme: (theme) => {\n        set({ theme });\n        \n        // Apply theme to document\n        if (typeof window !== 'undefined') {\n          const root = window.document.documentElement;\n          root.classList.remove('light', 'dark');\n          \n          if (theme === 'system') {\n            const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';\n            root.classList.add(systemTheme);\n          } else {\n            root.classList.add(theme);\n          }\n        }\n      },\n\n      toggleSidebar: () => {\n        set((state) => ({ sidebarCollapsed: !state.sidebarCollapsed }));\n      },\n\n      setSidebarOpen: (sidebarOpen) => {\n        set({ sidebarOpen });\n      },\n\n      setSidebarCollapsed: (sidebarCollapsed) => {\n        set({ sidebarCollapsed });\n      },\n\n      // Loading Actions\n      setGlobalLoading: (globalLoading, loadingMessage = '') => {\n        set({ globalLoading, loadingMessage });\n      },\n\n      // Notification Actions\n      addNotification: (notificationData) => {\n        const notification: Notification = {\n          ...notificationData,\n          id: generateNotificationId(),\n          createdAt: new Date(),\n        };\n\n        set((state) => ({\n          notifications: [...state.notifications, notification],\n        }));\n\n        // Auto-remove notification after duration (if specified)\n        if (notification.duration && notification.duration > 0) {\n          setTimeout(() => {\n            get().removeNotification(notification.id);\n          }, notification.duration);\n        }\n      },\n\n      removeNotification: (id) => {\n        set((state) => ({\n          notifications: state.notifications.filter((n) => n.id !== id),\n        }));\n      },\n\n      clearNotifications: () => {\n        set({ notifications: [] });\n      },\n\n      // Modal Actions\n      openModal: (modalId) => {\n        set((state) => ({\n          modals: { ...state.modals, [modalId]: true },\n        }));\n      },\n\n      closeModal: (modalId) => {\n        set((state) => ({\n          modals: { ...state.modals, [modalId]: false },\n        }));\n      },\n\n      toggleModal: (modalId) => {\n        set((state) => ({\n          modals: { ...state.modals, [modalId]: !state.modals[modalId] },\n        }));\n      },\n\n      isModalOpen: (modalId) => {\n        return get().modals[modalId] || false;\n      },\n\n      // Page State Actions\n      setPageTitle: (pageTitle) => {\n        set({ pageTitle });\n        \n        // Update document title\n        if (typeof window !== 'undefined') {\n          document.title = `${pageTitle} | Dentist Appointment Platform`;\n        }\n      },\n\n      setBreadcrumbs: (breadcrumbs) => {\n        set({ breadcrumbs });\n      },\n\n      addBreadcrumb: (breadcrumb) => {\n        set((state) => ({\n          breadcrumbs: [...state.breadcrumbs, breadcrumb],\n        }));\n      },\n\n      reset: () => {\n        set({\n          sidebarCollapsed: false,\n          sidebarOpen: false,\n          globalLoading: false,\n          loadingMessage: '',\n          notifications: [],\n          modals: {},\n          pageTitle: 'Dentist Appointment Platform',\n          breadcrumbs: [],\n        });\n      },\n    }),\n    {\n      name: 'ui-storage',\n      storage: createJSONStorage(() => localStorage),\n      partialize: (state) => ({\n        theme: state.theme,\n        sidebarCollapsed: state.sidebarCollapsed,\n      }),\n    }\n  )\n);\n\n// Convenience hooks for specific UI features\nexport const useTheme = () => {\n  const theme = useUIStore((state) => state.theme);\n  const setTheme = useUIStore((state) => state.setTheme);\n  return { theme, setTheme };\n};\n\nexport const useSidebar = () => {\n  const sidebarCollapsed = useUIStore((state) => state.sidebarCollapsed);\n  const sidebarOpen = useUIStore((state) => state.sidebarOpen);\n  const toggleSidebar = useUIStore((state) => state.toggleSidebar);\n  const setSidebarOpen = useUIStore((state) => state.setSidebarOpen);\n  const setSidebarCollapsed = useUIStore((state) => state.setSidebarCollapsed);\n  \n  return {\n    sidebarCollapsed,\n    sidebarOpen,\n    toggleSidebar,\n    setSidebarOpen,\n    setSidebarCollapsed,\n  };\n};\n\nexport const useNotifications = () => {\n  const notifications = useUIStore((state) => state.notifications);\n  const addNotification = useUIStore((state) => state.addNotification);\n  const removeNotification = useUIStore((state) => state.removeNotification);\n  const clearNotifications = useUIStore((state) => state.clearNotifications);\n  \n  // Convenience methods for different notification types\n  const showSuccess = (title: string, message?: string, duration = 5000) => {\n    addNotification({\n      type: NotificationType.SUCCESS,\n      title,\n      message,\n      duration,\n    });\n  };\n\n  const showError = (title: string, message?: string, duration = 0) => {\n    addNotification({\n      type: NotificationType.ERROR,\n      title,\n      message,\n      duration,\n    });\n  };\n\n  const showWarning = (title: string, message?: string, duration = 7000) => {\n    addNotification({\n      type: NotificationType.WARNING,\n      title,\n      message,\n      duration,\n    });\n  };\n\n  const showInfo = (title: string, message?: string, duration = 5000) => {\n    addNotification({\n      type: NotificationType.INFO,\n      title,\n      message,\n      duration,\n    });\n  };\n  \n  return {\n    notifications,\n    addNotification,\n    removeNotification,\n    clearNotifications,\n    showSuccess,\n    showError,\n    showWarning,\n    showInfo,\n  };\n};\n\nexport const useModals = () => {\n  const modals = useUIStore((state) => state.modals);\n  const openModal = useUIStore((state) => state.openModal);\n  const closeModal = useUIStore((state) => state.closeModal);\n  const toggleModal = useUIStore((state) => state.toggleModal);\n  const isModalOpen = useUIStore((state) => state.isModalOpen);\n  \n  return {\n    modals,\n    openModal,\n    closeModal,\n    toggleModal,\n    isModalOpen,\n  };\n};\n\nexport const usePageState = () => {\n  const pageTitle = useUIStore((state) => state.pageTitle);\n  const breadcrumbs = useUIStore((state) => state.breadcrumbs);\n  const setPageTitle = useUIStore((state) => state.setPageTitle);\n  const setBreadcrumbs = useUIStore((state) => state.setBreadcrumbs);\n  const addBreadcrumb = useUIStore((state) => state.addBreadcrumb);\n  \n  return {\n    pageTitle,\n    breadcrumbs,\n    setPageTitle,\n    setBreadcrumbs,\n    addBreadcrumb,\n  };\n};\n"], "names": [], "mappings": ";;;;;;;;AAAA;AACA;AACA;AAAA;;;;AAoDA,8CAA8C;AAC9C,MAAM,yBAAyB;IAC7B,OAAO,CAAC,aAAa,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;AAChF;AAEO,MAAM,aAAa,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC7B,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,gBAAgB;QAChB,OAAO;QACP,kBAAkB;QAClB,aAAa;QACb,eAAe;QACf,gBAAgB;QAChB,eAAe,EAAE;QACjB,QAAQ,CAAC;QACT,WAAW;QACX,aAAa,EAAE;QAEf,2BAA2B;QAC3B,UAAU,CAAC;YACT,IAAI;gBAAE;YAAM;YAEZ,0BAA0B;YAC1B,uCAAmC;;YAUnC;QACF;QAEA,eAAe;YACb,IAAI,CAAC,QAAU,CAAC;oBAAE,kBAAkB,CAAC,MAAM,gBAAgB;gBAAC,CAAC;QAC/D;QAEA,gBAAgB,CAAC;YACf,IAAI;gBAAE;YAAY;QACpB;QAEA,qBAAqB,CAAC;YACpB,IAAI;gBAAE;YAAiB;QACzB;QAEA,kBAAkB;QAClB,kBAAkB,CAAC,eAAe,iBAAiB,EAAE;YACnD,IAAI;gBAAE;gBAAe;YAAe;QACtC;QAEA,uBAAuB;QACvB,iBAAiB,CAAC;YAChB,MAAM,eAA6B;gBACjC,GAAG,gBAAgB;gBACnB,IAAI;gBACJ,WAAW,IAAI;YACjB;YAEA,IAAI,CAAC,QAAU,CAAC;oBACd,eAAe;2BAAI,MAAM,aAAa;wBAAE;qBAAa;gBACvD,CAAC;YAED,yDAAyD;YACzD,IAAI,aAAa,QAAQ,IAAI,aAAa,QAAQ,GAAG,GAAG;gBACtD,WAAW;oBACT,MAAM,kBAAkB,CAAC,aAAa,EAAE;gBAC1C,GAAG,aAAa,QAAQ;YAC1B;QACF;QAEA,oBAAoB,CAAC;YACnB,IAAI,CAAC,QAAU,CAAC;oBACd,eAAe,MAAM,aAAa,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;gBAC5D,CAAC;QACH;QAEA,oBAAoB;YAClB,IAAI;gBAAE,eAAe,EAAE;YAAC;QAC1B;QAEA,gBAAgB;QAChB,WAAW,CAAC;YACV,IAAI,CAAC,QAAU,CAAC;oBACd,QAAQ;wBAAE,GAAG,MAAM,MAAM;wBAAE,CAAC,QAAQ,EAAE;oBAAK;gBAC7C,CAAC;QACH;QAEA,YAAY,CAAC;YACX,IAAI,CAAC,QAAU,CAAC;oBACd,QAAQ;wBAAE,GAAG,MAAM,MAAM;wBAAE,CAAC,QAAQ,EAAE;oBAAM;gBAC9C,CAAC;QACH;QAEA,aAAa,CAAC;YACZ,IAAI,CAAC,QAAU,CAAC;oBACd,QAAQ;wBAAE,GAAG,MAAM,MAAM;wBAAE,CAAC,QAAQ,EAAE,CAAC,MAAM,MAAM,CAAC,QAAQ;oBAAC;gBAC/D,CAAC;QACH;QAEA,aAAa,CAAC;YACZ,OAAO,MAAM,MAAM,CAAC,QAAQ,IAAI;QAClC;QAEA,qBAAqB;QACrB,cAAc,CAAC;YACb,IAAI;gBAAE;YAAU;YAEhB,wBAAwB;YACxB,uCAAmC;;YAEnC;QACF;QAEA,gBAAgB,CAAC;YACf,IAAI;gBAAE;YAAY;QACpB;QAEA,eAAe,CAAC;YACd,IAAI,CAAC,QAAU,CAAC;oBACd,aAAa;2BAAI,MAAM,WAAW;wBAAE;qBAAW;gBACjD,CAAC;QACH;QAEA,OAAO;YACL,IAAI;gBACF,kBAAkB;gBAClB,aAAa;gBACb,eAAe;gBACf,gBAAgB;gBAChB,eAAe,EAAE;gBACjB,QAAQ,CAAC;gBACT,WAAW;gBACX,aAAa,EAAE;YACjB;QACF;IACF,CAAC,GACD;IACE,MAAM;IACN,SAAS,CAAA,GAAA,6IAAA,CAAA,oBAAiB,AAAD,EAAE,IAAM;IACjC,YAAY,CAAC,QAAU,CAAC;YACtB,OAAO,MAAM,KAAK;YAClB,kBAAkB,MAAM,gBAAgB;QAC1C,CAAC;AACH;AAKG,MAAM,WAAW;IACtB,MAAM,QAAQ,WAAW,CAAC,QAAU,MAAM,KAAK;IAC/C,MAAM,WAAW,WAAW,CAAC,QAAU,MAAM,QAAQ;IACrD,OAAO;QAAE;QAAO;IAAS;AAC3B;AAEO,MAAM,aAAa;IACxB,MAAM,mBAAmB,WAAW,CAAC,QAAU,MAAM,gBAAgB;IACrE,MAAM,cAAc,WAAW,CAAC,QAAU,MAAM,WAAW;IAC3D,MAAM,gBAAgB,WAAW,CAAC,QAAU,MAAM,aAAa;IAC/D,MAAM,iBAAiB,WAAW,CAAC,QAAU,MAAM,cAAc;IACjE,MAAM,sBAAsB,WAAW,CAAC,QAAU,MAAM,mBAAmB;IAE3E,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;AAEO,MAAM,mBAAmB;IAC9B,MAAM,gBAAgB,WAAW,CAAC,QAAU,MAAM,aAAa;IAC/D,MAAM,kBAAkB,WAAW,CAAC,QAAU,MAAM,eAAe;IACnE,MAAM,qBAAqB,WAAW,CAAC,QAAU,MAAM,kBAAkB;IACzE,MAAM,qBAAqB,WAAW,CAAC,QAAU,MAAM,kBAAkB;IAEzE,uDAAuD;IACvD,MAAM,cAAc,CAAC,OAAe,SAAkB,WAAW,IAAI;QACnE,gBAAgB;YACd,MAAM,sIAAA,CAAA,mBAAgB,CAAC,OAAO;YAC9B;YACA;YACA;QACF;IACF;IAEA,MAAM,YAAY,CAAC,OAAe,SAAkB,WAAW,CAAC;QAC9D,gBAAgB;YACd,MAAM,sIAAA,CAAA,mBAAgB,CAAC,KAAK;YAC5B;YACA;YACA;QACF;IACF;IAEA,MAAM,cAAc,CAAC,OAAe,SAAkB,WAAW,IAAI;QACnE,gBAAgB;YACd,MAAM,sIAAA,CAAA,mBAAgB,CAAC,OAAO;YAC9B;YACA;YACA;QACF;IACF;IAEA,MAAM,WAAW,CAAC,OAAe,SAAkB,WAAW,IAAI;QAChE,gBAAgB;YACd,MAAM,sIAAA,CAAA,mBAAgB,CAAC,IAAI;YAC3B;YACA;YACA;QACF;IACF;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;AAEO,MAAM,YAAY;IACvB,MAAM,SAAS,WAAW,CAAC,QAAU,MAAM,MAAM;IACjD,MAAM,YAAY,WAAW,CAAC,QAAU,MAAM,SAAS;IACvD,MAAM,aAAa,WAAW,CAAC,QAAU,MAAM,UAAU;IACzD,MAAM,cAAc,WAAW,CAAC,QAAU,MAAM,WAAW;IAC3D,MAAM,cAAc,WAAW,CAAC,QAAU,MAAM,WAAW;IAE3D,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;AAEO,MAAM,eAAe;IAC1B,MAAM,YAAY,WAAW,CAAC,QAAU,MAAM,SAAS;IACvD,MAAM,cAAc,WAAW,CAAC,QAAU,MAAM,WAAW;IAC3D,MAAM,eAAe,WAAW,CAAC,QAAU,MAAM,YAAY;IAC7D,MAAM,iBAAiB,WAAW,CAAC,QAAU,MAAM,cAAc;IACjE,MAAM,gBAAgB,WAAW,CAAC,QAAU,MAAM,aAAa;IAE/D,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 1023, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/test/dentist-appointment-platform/src/store/authStore.ts"], "sourcesContent": ["import { create } from 'zustand';\nimport { persist, createJSONStorage } from 'zustand/middleware';\nimport { User, UserRole, AuthSession } from '@/types/auth';\n\ninterface AuthState {\n  // State\n  user: User | null;\n  session: AuthSession | null;\n  isAuthenticated: boolean;\n  isLoading: boolean;\n  error: string | null;\n  \n  // Actions\n  setUser: (user: User | null) => void;\n  setSession: (session: AuthSession | null) => void;\n  setLoading: (loading: boolean) => void;\n  setError: (error: string | null) => void;\n  login: (email: string, password: string) => Promise<void>;\n  logout: () => void;\n  refreshToken: () => Promise<void>;\n  updateProfile: (data: Partial<User>) => Promise<void>;\n  checkPermission: (permission: string) => boolean;\n  hasRole: (role: UserRole) => boolean;\n  clearError: () => void;\n  reset: () => void;\n}\n\n// Role-based permissions mapping\nconst ROLE_PERMISSIONS = {\n  [UserRole.PATIENT]: [\n    'appointments:read:own',\n    'appointments:create:own',\n    'appointments:update:own',\n    'documents:read:own',\n    'profile:read:own',\n    'profile:update:own'\n  ],\n  [UserRole.DENTIST]: [\n    'appointments:read:all',\n    'appointments:create:all',\n    'appointments:update:all',\n    'appointments:delete:all',\n    'patients:read:all',\n    'patients:update:all',\n    'documents:read:all',\n    'documents:create:all',\n    'documents:update:all',\n    'documents:delete:all',\n    'profile:read:own',\n    'profile:update:own'\n  ],\n  [UserRole.ADMIN]: [\n    '*' // All permissions\n  ]\n};\n\nexport const useAuthStore = create<AuthState>()(\n  persist(\n    (set, get) => ({\n      // Initial state\n      user: null,\n      session: null,\n      isAuthenticated: false,\n      isLoading: false,\n      error: null,\n\n      // Actions\n      setUser: (user) => {\n        set({ \n          user, \n          isAuthenticated: !!user,\n          error: null \n        });\n      },\n\n      setSession: (session) => {\n        set({ \n          session,\n          user: session?.user || null,\n          isAuthenticated: !!session?.user,\n          error: null\n        });\n      },\n\n      setLoading: (isLoading) => {\n        set({ isLoading });\n      },\n\n      setError: (error) => {\n        set({ error, isLoading: false });\n      },\n\n      login: async (email: string, password: string) => {\n        const { setLoading, setError, setSession } = get();\n        \n        try {\n          setLoading(true);\n          setError(null);\n\n          // TODO: Replace with actual API call\n          const response = await fetch('/api/auth/login', {\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/json',\n            },\n            body: JSON.stringify({ email, password }),\n          });\n\n          if (!response.ok) {\n            throw new Error('Login failed');\n          }\n\n          const data = await response.json();\n          \n          if (data.success && data.user && data.token) {\n            const session: AuthSession = {\n              user: data.user,\n              accessToken: data.token,\n              expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours\n            };\n            \n            setSession(session);\n          } else {\n            throw new Error(data.message || 'Login failed');\n          }\n        } catch (error) {\n          setError(error instanceof Error ? error.message : 'Login failed');\n        } finally {\n          setLoading(false);\n        }\n      },\n\n      logout: () => {\n        // Clear session from server if needed\n        fetch('/api/auth/logout', { method: 'POST' }).catch(() => {\n          // Ignore errors during logout\n        });\n\n        set({\n          user: null,\n          session: null,\n          isAuthenticated: false,\n          error: null,\n        });\n      },\n\n      refreshToken: async () => {\n        const { session, setSession, setError } = get();\n        \n        if (!session?.refreshToken) {\n          return;\n        }\n\n        try {\n          const response = await fetch('/api/auth/refresh', {\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/json',\n            },\n            body: JSON.stringify({ refreshToken: session.refreshToken }),\n          });\n\n          if (!response.ok) {\n            throw new Error('Token refresh failed');\n          }\n\n          const data = await response.json();\n          \n          if (data.success && data.token) {\n            const newSession: AuthSession = {\n              ...session,\n              accessToken: data.token,\n              expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000),\n            };\n            \n            setSession(newSession);\n          } else {\n            throw new Error('Token refresh failed');\n          }\n        } catch (error) {\n          setError(error instanceof Error ? error.message : 'Token refresh failed');\n          // If refresh fails, logout user\n          get().logout();\n        }\n      },\n\n      updateProfile: async (data: Partial<User>) => {\n        const { user, session, setUser, setError, setLoading } = get();\n        \n        if (!user || !session) {\n          setError('User not authenticated');\n          return;\n        }\n\n        try {\n          setLoading(true);\n          setError(null);\n\n          const response = await fetch('/api/user/profile', {\n            method: 'PATCH',\n            headers: {\n              'Content-Type': 'application/json',\n              'Authorization': `Bearer ${session.accessToken}`,\n            },\n            body: JSON.stringify(data),\n          });\n\n          if (!response.ok) {\n            throw new Error('Profile update failed');\n          }\n\n          const result = await response.json();\n          \n          if (result.success && result.user) {\n            setUser(result.user);\n          } else {\n            throw new Error(result.message || 'Profile update failed');\n          }\n        } catch (error) {\n          setError(error instanceof Error ? error.message : 'Profile update failed');\n        } finally {\n          setLoading(false);\n        }\n      },\n\n      checkPermission: (permission: string) => {\n        const { user } = get();\n        \n        if (!user) return false;\n        \n        const userPermissions = ROLE_PERMISSIONS[user.role] || [];\n        \n        // Admin has all permissions\n        if (userPermissions.includes('*')) return true;\n        \n        return userPermissions.includes(permission);\n      },\n\n      hasRole: (role: UserRole) => {\n        const { user } = get();\n        return user?.role === role;\n      },\n\n      clearError: () => {\n        set({ error: null });\n      },\n\n      reset: () => {\n        set({\n          user: null,\n          session: null,\n          isAuthenticated: false,\n          isLoading: false,\n          error: null,\n        });\n      },\n    }),\n    {\n      name: 'auth-storage',\n      storage: createJSONStorage(() => localStorage),\n      partialize: (state) => ({\n        user: state.user,\n        session: state.session,\n        isAuthenticated: state.isAuthenticated,\n      }),\n    }\n  )\n);\n\n// Selectors for easier access\nexport const useAuth = () => {\n  const store = useAuthStore();\n  return {\n    user: store.user,\n    session: store.session,\n    isAuthenticated: store.isAuthenticated,\n    isLoading: store.isLoading,\n    error: store.error,\n  };\n};\n\nexport const useAuthActions = () => {\n  const store = useAuthStore();\n  return {\n    login: store.login,\n    logout: store.logout,\n    refreshToken: store.refreshToken,\n    updateProfile: store.updateProfile,\n    checkPermission: store.checkPermission,\n    hasRole: store.hasRole,\n    clearError: store.clearError,\n    reset: store.reset,\n  };\n};\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;AAyBA,iCAAiC;AACjC,MAAM,mBAAmB;IACvB,CAAC,oHAAA,CAAA,WAAQ,CAAC,OAAO,CAAC,EAAE;QAClB;QACA;QACA;QACA;QACA;QACA;KACD;IACD,CAAC,oHAAA,CAAA,WAAQ,CAAC,OAAO,CAAC,EAAE;QAClB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IACD,CAAC,oHAAA,CAAA,WAAQ,CAAC,KAAK,CAAC,EAAE;QAChB,IAAI,kBAAkB;KACvB;AACH;AAEO,MAAM,eAAe,CAAA,GAAA,wIAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,gBAAgB;QAChB,MAAM;QACN,SAAS;QACT,iBAAiB;QACjB,WAAW;QACX,OAAO;QAEP,UAAU;QACV,SAAS,CAAC;YACR,IAAI;gBACF;gBACA,iBAAiB,CAAC,CAAC;gBACnB,OAAO;YACT;QACF;QAEA,YAAY,CAAC;YACX,IAAI;gBACF;gBACA,MAAM,SAAS,QAAQ;gBACvB,iBAAiB,CAAC,CAAC,SAAS;gBAC5B,OAAO;YACT;QACF;QAEA,YAAY,CAAC;YACX,IAAI;gBAAE;YAAU;QAClB;QAEA,UAAU,CAAC;YACT,IAAI;gBAAE;gBAAO,WAAW;YAAM;QAChC;QAEA,OAAO,OAAO,OAAe;YAC3B,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG;YAE7C,IAAI;gBACF,WAAW;gBACX,SAAS;gBAET,qCAAqC;gBACrC,MAAM,WAAW,MAAM,MAAM,mBAAmB;oBAC9C,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;wBAAE;wBAAO;oBAAS;gBACzC;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,IAAI,KAAK,OAAO,IAAI,KAAK,IAAI,IAAI,KAAK,KAAK,EAAE;oBAC3C,MAAM,UAAuB;wBAC3B,MAAM,KAAK,IAAI;wBACf,aAAa,KAAK,KAAK;wBACvB,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK;oBAClD;oBAEA,WAAW;gBACb,OAAO;oBACL,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI;gBAClC;YACF,EAAE,OAAO,OAAO;gBACd,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD,SAAU;gBACR,WAAW;YACb;QACF;QAEA,QAAQ;YACN,sCAAsC;YACtC,MAAM,oBAAoB;gBAAE,QAAQ;YAAO,GAAG,KAAK,CAAC;YAClD,8BAA8B;YAChC;YAEA,IAAI;gBACF,MAAM;gBACN,SAAS;gBACT,iBAAiB;gBACjB,OAAO;YACT;QACF;QAEA,cAAc;YACZ,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG;YAE1C,IAAI,CAAC,SAAS,cAAc;gBAC1B;YACF;YAEA,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,qBAAqB;oBAChD,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;wBAAE,cAAc,QAAQ,YAAY;oBAAC;gBAC5D;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,IAAI,KAAK,OAAO,IAAI,KAAK,KAAK,EAAE;oBAC9B,MAAM,aAA0B;wBAC9B,GAAG,OAAO;wBACV,aAAa,KAAK,KAAK;wBACvB,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK;oBAClD;oBAEA,WAAW;gBACb,OAAO;oBACL,MAAM,IAAI,MAAM;gBAClB;YACF,EAAE,OAAO,OAAO;gBACd,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAClD,gCAAgC;gBAChC,MAAM,MAAM;YACd;QACF;QAEA,eAAe,OAAO;YACpB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG;YAEzD,IAAI,CAAC,QAAQ,CAAC,SAAS;gBACrB,SAAS;gBACT;YACF;YAEA,IAAI;gBACF,WAAW;gBACX,SAAS;gBAET,MAAM,WAAW,MAAM,MAAM,qBAAqB;oBAChD,QAAQ;oBACR,SAAS;wBACP,gBAAgB;wBAChB,iBAAiB,CAAC,OAAO,EAAE,QAAQ,WAAW,EAAE;oBAClD;oBACA,MAAM,KAAK,SAAS,CAAC;gBACvB;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM;gBAClB;gBAEA,MAAM,SAAS,MAAM,SAAS,IAAI;gBAElC,IAAI,OAAO,OAAO,IAAI,OAAO,IAAI,EAAE;oBACjC,QAAQ,OAAO,IAAI;gBACrB,OAAO;oBACL,MAAM,IAAI,MAAM,OAAO,OAAO,IAAI;gBACpC;YACF,EAAE,OAAO,OAAO;gBACd,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD,SAAU;gBACR,WAAW;YACb;QACF;QAEA,iBAAiB,CAAC;YAChB,MAAM,EAAE,IAAI,EAAE,GAAG;YAEjB,IAAI,CAAC,MAAM,OAAO;YAElB,MAAM,kBAAkB,gBAAgB,CAAC,KAAK,IAAI,CAAC,IAAI,EAAE;YAEzD,4BAA4B;YAC5B,IAAI,gBAAgB,QAAQ,CAAC,MAAM,OAAO;YAE1C,OAAO,gBAAgB,QAAQ,CAAC;QAClC;QAEA,SAAS,CAAC;YACR,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,OAAO,MAAM,SAAS;QACxB;QAEA,YAAY;YACV,IAAI;gBAAE,OAAO;YAAK;QACpB;QAEA,OAAO;YACL,IAAI;gBACF,MAAM;gBACN,SAAS;gBACT,iBAAiB;gBACjB,WAAW;gBACX,OAAO;YACT;QACF;IACF,CAAC,GACD;IACE,MAAM;IACN,SAAS,CAAA,GAAA,6IAAA,CAAA,oBAAiB,AAAD,EAAE,IAAM;IACjC,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM,MAAM,IAAI;YAChB,SAAS,MAAM,OAAO;YACtB,iBAAiB,MAAM,eAAe;QACxC,CAAC;AACH;AAKG,MAAM,UAAU;IACrB,MAAM,QAAQ;IACd,OAAO;QACL,MAAM,MAAM,IAAI;QAChB,SAAS,MAAM,OAAO;QACtB,iBAAiB,MAAM,eAAe;QACtC,WAAW,MAAM,SAAS;QAC1B,OAAO,MAAM,KAAK;IACpB;AACF;AAEO,MAAM,iBAAiB;IAC5B,MAAM,QAAQ;IACd,OAAO;QACL,OAAO,MAAM,KAAK;QAClB,QAAQ,MAAM,MAAM;QACpB,cAAc,MAAM,YAAY;QAChC,eAAe,MAAM,aAAa;QAClC,iBAAiB,MAAM,eAAe;QACtC,SAAS,MAAM,OAAO;QACtB,YAAY,MAAM,UAAU;QAC5B,OAAO,MAAM,KAAK;IACpB;AACF", "debugId": null}}, {"offset": {"line": 1277, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/test/dentist-appointment-platform/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1334, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/test/dentist-appointment-platform/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1380, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/test/dentist-appointment-platform/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { usePathname } from 'next/navigation';\nimport { cn } from '@/lib/utils';\nimport { useSidebar } from '@/store/uiStore';\nimport { useAuth } from '@/store/authStore';\nimport { UserRole } from '@/types/auth';\nimport { Button } from '@/components/ui/button';\nimport { Badge } from '@/components/ui/badge';\nimport {\n  Calendar,\n  Users,\n  FileText,\n  Settings,\n  Home,\n  User,\n  Stethoscope,\n  ClipboardList,\n  MessageSquare,\n  BarChart3,\n  Shield,\n  HelpCircle,\n  ChevronLeft,\n  ChevronRight,\n} from 'lucide-react';\n\ninterface NavigationItem {\n  name: string;\n  href: string;\n  icon: React.ComponentType<{ className?: string }>;\n  badge?: string | number;\n  roles?: UserRole[];\n  children?: NavigationItem[];\n}\n\nconst navigation: NavigationItem[] = [\n  {\n    name: 'Dashboard',\n    href: '/dashboard',\n    icon: Home,\n    roles: [UserRole.PATIENT, UserRole.DENTIST, UserRole.ADMIN],\n  },\n  {\n    name: 'Appointments',\n    href: '/appointments',\n    icon: Calendar,\n    roles: [UserRole.PATIENT, UserRole.DENTIST, UserRole.ADMIN],\n    children: [\n      {\n        name: 'My Appointments',\n        href: '/appointments',\n        icon: Calendar,\n        roles: [UserRole.PATIENT],\n      },\n      {\n        name: 'Book Appointment',\n        href: '/appointments/book',\n        icon: Calendar,\n        roles: [UserRole.PATIENT],\n      },\n      {\n        name: 'All Appointments',\n        href: '/appointments',\n        icon: Calendar,\n        roles: [UserRole.DENTIST, UserRole.ADMIN],\n      },\n      {\n        name: 'Schedule',\n        href: '/appointments/schedule',\n        icon: ClipboardList,\n        roles: [UserRole.DENTIST, UserRole.ADMIN],\n      },\n    ],\n  },\n  {\n    name: 'Patients',\n    href: '/patients',\n    icon: Users,\n    roles: [UserRole.DENTIST, UserRole.ADMIN],\n    children: [\n      {\n        name: 'All Patients',\n        href: '/patients',\n        icon: Users,\n        roles: [UserRole.DENTIST, UserRole.ADMIN],\n      },\n      {\n        name: 'Add Patient',\n        href: '/patients/add',\n        icon: User,\n        roles: [UserRole.DENTIST, UserRole.ADMIN],\n      },\n    ],\n  },\n  {\n    name: 'Documents',\n    href: '/documents',\n    icon: FileText,\n    roles: [UserRole.PATIENT, UserRole.DENTIST, UserRole.ADMIN],\n    children: [\n      {\n        name: 'My Documents',\n        href: '/documents',\n        icon: FileText,\n        roles: [UserRole.PATIENT],\n      },\n      {\n        name: 'Patient Documents',\n        href: '/documents',\n        icon: FileText,\n        roles: [UserRole.DENTIST, UserRole.ADMIN],\n      },\n      {\n        name: 'Upload Document',\n        href: '/documents/upload',\n        icon: FileText,\n        roles: [UserRole.DENTIST, UserRole.ADMIN],\n      },\n    ],\n  },\n  {\n    name: 'Profile',\n    href: '/profile',\n    icon: User,\n    roles: [UserRole.PATIENT, UserRole.DENTIST, UserRole.ADMIN],\n  },\n  {\n    name: 'Messages',\n    href: '/messages',\n    icon: MessageSquare,\n    badge: 3,\n    roles: [UserRole.PATIENT, UserRole.DENTIST, UserRole.ADMIN],\n  },\n  {\n    name: 'Reports',\n    href: '/reports',\n    icon: BarChart3,\n    roles: [UserRole.DENTIST, UserRole.ADMIN],\n  },\n  {\n    name: 'Practice Management',\n    href: '/practice',\n    icon: Stethoscope,\n    roles: [UserRole.DENTIST, UserRole.ADMIN],\n    children: [\n      {\n        name: 'Working Hours',\n        href: '/practice/hours',\n        icon: Calendar,\n        roles: [UserRole.DENTIST, UserRole.ADMIN],\n      },\n      {\n        name: 'Services',\n        href: '/practice/services',\n        icon: ClipboardList,\n        roles: [UserRole.DENTIST, UserRole.ADMIN],\n      },\n      {\n        name: 'Staff',\n        href: '/practice/staff',\n        icon: Users,\n        roles: [UserRole.ADMIN],\n      },\n    ],\n  },\n  {\n    name: 'Administration',\n    href: '/admin',\n    icon: Shield,\n    roles: [UserRole.ADMIN],\n    children: [\n      {\n        name: 'User Management',\n        href: '/admin/users',\n        icon: Users,\n        roles: [UserRole.ADMIN],\n      },\n      {\n        name: 'System Settings',\n        href: '/admin/settings',\n        icon: Settings,\n        roles: [UserRole.ADMIN],\n      },\n      {\n        name: 'Audit Logs',\n        href: '/admin/audit',\n        icon: FileText,\n        roles: [UserRole.ADMIN],\n      },\n    ],\n  },\n];\n\nconst bottomNavigation: NavigationItem[] = [\n  {\n    name: 'Settings',\n    href: '/settings',\n    icon: Settings,\n    roles: [UserRole.PATIENT, UserRole.DENTIST, UserRole.ADMIN],\n  },\n  {\n    name: 'Help & Support',\n    href: '/help',\n    icon: HelpCircle,\n    roles: [UserRole.PATIENT, UserRole.DENTIST, UserRole.ADMIN],\n  },\n];\n\nconst Sidebar: React.FC = () => {\n  const pathname = usePathname();\n  const { sidebarCollapsed, sidebarOpen, setSidebarOpen, toggleSidebar } = useSidebar();\n  const { user } = useAuth();\n\n  const filterNavigationByRole = (items: NavigationItem[]): NavigationItem[] => {\n    if (!user) return [];\n\n    return items.filter(item => {\n      if (!item.roles || item.roles.includes(user.role)) {\n        if (item.children) {\n          item.children = filterNavigationByRole(item.children);\n        }\n        return true;\n      }\n      return false;\n    });\n  };\n\n  const filteredNavigation = filterNavigationByRole(navigation);\n  const filteredBottomNavigation = filterNavigationByRole(bottomNavigation);\n\n  const isActive = (href: string) => {\n    if (href === '/dashboard') {\n      return pathname === '/dashboard' || pathname === '/';\n    }\n    return pathname?.startsWith(href);\n  };\n\n  const NavItem: React.FC<{ item: NavigationItem; level?: number }> = ({ \n    item, \n    level = 0 \n  }) => {\n    const active = isActive(item.href);\n    const hasChildren = item.children && item.children.length > 0;\n    const isParentActive = hasChildren && item.children.some(child => isActive(child.href));\n\n    return (\n      <div>\n        <Link\n          href={item.href}\n          className={cn(\n            'group flex items-center rounded-lg px-3 py-2 text-sm font-medium transition-all hover:bg-accent hover:text-accent-foreground',\n            level > 0 && 'ml-4 pl-6',\n            active || isParentActive\n              ? 'bg-accent text-accent-foreground'\n              : 'text-muted-foreground',\n            sidebarCollapsed && level === 0 && 'justify-center px-2'\n          )}\n          onClick={() => {\n            if (window.innerWidth < 768) {\n              setSidebarOpen(false);\n            }\n          }}\n        >\n          <item.icon\n            className={cn(\n              'h-5 w-5 shrink-0',\n              sidebarCollapsed && level === 0 ? 'mr-0' : 'mr-3'\n            )}\n          />\n          {(!sidebarCollapsed || level > 0) && (\n            <>\n              <span className=\"truncate\">{item.name}</span>\n              {item.badge && (\n                <Badge\n                  variant=\"secondary\"\n                  className=\"ml-auto h-5 w-5 rounded-full p-0 text-xs\"\n                >\n                  {item.badge}\n                </Badge>\n              )}\n            </>\n          )}\n        </Link>\n\n        {/* Render children if not collapsed or if it's a nested item */}\n        {hasChildren && (!sidebarCollapsed || level > 0) && (\n          <div className=\"mt-1 space-y-1\">\n            {item.children!.map((child) => (\n              <NavItem key={child.href} item={child} level={level + 1} />\n            ))}\n          </div>\n        )}\n      </div>\n    );\n  };\n\n  return (\n    <>\n      {/* Desktop Sidebar */}\n      <div\n        className={cn(\n          'fixed left-0 top-0 z-50 h-full bg-background border-r border-border transition-all duration-300 ease-in-out hidden md:flex flex-col',\n          sidebarCollapsed ? 'w-20' : 'w-72'\n        )}\n      >\n        {/* Logo and toggle */}\n        <div className=\"flex h-16 items-center justify-between px-4 border-b border-border\">\n          {!sidebarCollapsed && (\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"flex h-8 w-8 items-center justify-center rounded-lg bg-primary\">\n                <Stethoscope className=\"h-5 w-5 text-primary-foreground\" />\n              </div>\n              <span className=\"text-lg font-semibold\">DentistApp</span>\n            </div>\n          )}\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            onClick={toggleSidebar}\n            className=\"h-8 w-8\"\n          >\n            {sidebarCollapsed ? (\n              <ChevronRight className=\"h-4 w-4\" />\n            ) : (\n              <ChevronLeft className=\"h-4 w-4\" />\n            )}\n          </Button>\n        </div>\n\n        {/* Navigation */}\n        <nav className=\"flex-1 space-y-2 p-4 overflow-y-auto\">\n          {filteredNavigation.map((item) => (\n            <NavItem key={item.href} item={item} />\n          ))}\n        </nav>\n\n        {/* Bottom navigation */}\n        <div className=\"border-t border-border p-4 space-y-2\">\n          {filteredBottomNavigation.map((item) => (\n            <NavItem key={item.href} item={item} />\n          ))}\n        </div>\n      </div>\n\n      {/* Mobile Sidebar */}\n      <div\n        className={cn(\n          'fixed left-0 top-0 z-50 h-full w-72 bg-background border-r border-border transition-transform duration-300 ease-in-out md:hidden flex flex-col',\n          sidebarOpen ? 'translate-x-0' : '-translate-x-full'\n        )}\n      >\n        {/* Logo */}\n        <div className=\"flex h-16 items-center px-4 border-b border-border\">\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"flex h-8 w-8 items-center justify-center rounded-lg bg-primary\">\n              <Stethoscope className=\"h-5 w-5 text-primary-foreground\" />\n            </div>\n            <span className=\"text-lg font-semibold\">DentistApp</span>\n          </div>\n        </div>\n\n        {/* Navigation */}\n        <nav className=\"flex-1 space-y-2 p-4 overflow-y-auto\">\n          {filteredNavigation.map((item) => (\n            <NavItem key={item.href} item={item} />\n          ))}\n        </nav>\n\n        {/* Bottom navigation */}\n        <div className=\"border-t border-border p-4 space-y-2\">\n          {filteredBottomNavigation.map((item) => (\n            <NavItem key={item.href} item={item} />\n          ))}\n        </div>\n      </div>\n    </>\n  );\n};\n\nexport default Sidebar;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAXA;;;;;;;;;;;AAqCA,MAAM,aAA+B;IACnC;QACE,MAAM;QACN,MAAM;QACN,MAAM,mMAAA,CAAA,OAAI;QACV,OAAO;YAAC,oHAAA,CAAA,WAAQ,CAAC,OAAO;YAAE,oHAAA,CAAA,WAAQ,CAAC,OAAO;YAAE,oHAAA,CAAA,WAAQ,CAAC,KAAK;SAAC;IAC7D;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,0MAAA,CAAA,WAAQ;QACd,OAAO;YAAC,oHAAA,CAAA,WAAQ,CAAC,OAAO;YAAE,oHAAA,CAAA,WAAQ,CAAC,OAAO;YAAE,oHAAA,CAAA,WAAQ,CAAC,KAAK;SAAC;QAC3D,UAAU;YACR;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,0MAAA,CAAA,WAAQ;gBACd,OAAO;oBAAC,oHAAA,CAAA,WAAQ,CAAC,OAAO;iBAAC;YAC3B;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,0MAAA,CAAA,WAAQ;gBACd,OAAO;oBAAC,oHAAA,CAAA,WAAQ,CAAC,OAAO;iBAAC;YAC3B;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,0MAAA,CAAA,WAAQ;gBACd,OAAO;oBAAC,oHAAA,CAAA,WAAQ,CAAC,OAAO;oBAAE,oHAAA,CAAA,WAAQ,CAAC,KAAK;iBAAC;YAC3C;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,wNAAA,CAAA,gBAAa;gBACnB,OAAO;oBAAC,oHAAA,CAAA,WAAQ,CAAC,OAAO;oBAAE,oHAAA,CAAA,WAAQ,CAAC,KAAK;iBAAC;YAC3C;SACD;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;YAAC,oHAAA,CAAA,WAAQ,CAAC,OAAO;YAAE,oHAAA,CAAA,WAAQ,CAAC,KAAK;SAAC;QACzC,UAAU;YACR;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,oMAAA,CAAA,QAAK;gBACX,OAAO;oBAAC,oHAAA,CAAA,WAAQ,CAAC,OAAO;oBAAE,oHAAA,CAAA,WAAQ,CAAC,KAAK;iBAAC;YAC3C;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,kMAAA,CAAA,OAAI;gBACV,OAAO;oBAAC,oHAAA,CAAA,WAAQ,CAAC,OAAO;oBAAE,oHAAA,CAAA,WAAQ,CAAC,KAAK;iBAAC;YAC3C;SACD;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,8MAAA,CAAA,WAAQ;QACd,OAAO;YAAC,oHAAA,CAAA,WAAQ,CAAC,OAAO;YAAE,oHAAA,CAAA,WAAQ,CAAC,OAAO;YAAE,oHAAA,CAAA,WAAQ,CAAC,KAAK;SAAC;QAC3D,UAAU;YACR;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,8MAAA,CAAA,WAAQ;gBACd,OAAO;oBAAC,oHAAA,CAAA,WAAQ,CAAC,OAAO;iBAAC;YAC3B;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,8MAAA,CAAA,WAAQ;gBACd,OAAO;oBAAC,oHAAA,CAAA,WAAQ,CAAC,OAAO;oBAAE,oHAAA,CAAA,WAAQ,CAAC,KAAK;iBAAC;YAC3C;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,8MAAA,CAAA,WAAQ;gBACd,OAAO;oBAAC,oHAAA,CAAA,WAAQ,CAAC,OAAO;oBAAE,oHAAA,CAAA,WAAQ,CAAC,KAAK;iBAAC;YAC3C;SACD;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,kMAAA,CAAA,OAAI;QACV,OAAO;YAAC,oHAAA,CAAA,WAAQ,CAAC,OAAO;YAAE,oHAAA,CAAA,WAAQ,CAAC,OAAO;YAAE,oHAAA,CAAA,WAAQ,CAAC,KAAK;SAAC;IAC7D;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,wNAAA,CAAA,gBAAa;QACnB,OAAO;QACP,OAAO;YAAC,oHAAA,CAAA,WAAQ,CAAC,OAAO;YAAE,oHAAA,CAAA,WAAQ,CAAC,OAAO;YAAE,oHAAA,CAAA,WAAQ,CAAC,KAAK;SAAC;IAC7D;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,kNAAA,CAAA,YAAS;QACf,OAAO;YAAC,oHAAA,CAAA,WAAQ,CAAC,OAAO;YAAE,oHAAA,CAAA,WAAQ,CAAC,KAAK;SAAC;IAC3C;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,gNAAA,CAAA,cAAW;QACjB,OAAO;YAAC,oHAAA,CAAA,WAAQ,CAAC,OAAO;YAAE,oHAAA,CAAA,WAAQ,CAAC,KAAK;SAAC;QACzC,UAAU;YACR;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,0MAAA,CAAA,WAAQ;gBACd,OAAO;oBAAC,oHAAA,CAAA,WAAQ,CAAC,OAAO;oBAAE,oHAAA,CAAA,WAAQ,CAAC,KAAK;iBAAC;YAC3C;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,wNAAA,CAAA,gBAAa;gBACnB,OAAO;oBAAC,oHAAA,CAAA,WAAQ,CAAC,OAAO;oBAAE,oHAAA,CAAA,WAAQ,CAAC,KAAK;iBAAC;YAC3C;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,oMAAA,CAAA,QAAK;gBACX,OAAO;oBAAC,oHAAA,CAAA,WAAQ,CAAC,KAAK;iBAAC;YACzB;SACD;IACH;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;YAAC,oHAAA,CAAA,WAAQ,CAAC,KAAK;SAAC;QACvB,UAAU;YACR;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,oMAAA,CAAA,QAAK;gBACX,OAAO;oBAAC,oHAAA,CAAA,WAAQ,CAAC,KAAK;iBAAC;YACzB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,0MAAA,CAAA,WAAQ;gBACd,OAAO;oBAAC,oHAAA,CAAA,WAAQ,CAAC,KAAK;iBAAC;YACzB;YACA;gBACE,MAAM;gBACN,MAAM;gBACN,MAAM,8MAAA,CAAA,WAAQ;gBACd,OAAO;oBAAC,oHAAA,CAAA,WAAQ,CAAC,KAAK;iBAAC;YACzB;SACD;IACH;CACD;AAED,MAAM,mBAAqC;IACzC;QACE,MAAM;QACN,MAAM;QACN,MAAM,0MAAA,CAAA,WAAQ;QACd,OAAO;YAAC,oHAAA,CAAA,WAAQ,CAAC,OAAO;YAAE,oHAAA,CAAA,WAAQ,CAAC,OAAO;YAAE,oHAAA,CAAA,WAAQ,CAAC,KAAK;SAAC;IAC7D;IACA;QACE,MAAM;QACN,MAAM;QACN,MAAM,kNAAA,CAAA,aAAU;QAChB,OAAO;YAAC,oHAAA,CAAA,WAAQ,CAAC,OAAO;YAAE,oHAAA,CAAA,WAAQ,CAAC,OAAO;YAAE,oHAAA,CAAA,WAAQ,CAAC,KAAK;SAAC;IAC7D;CACD;AAED,MAAM,UAAoB;IACxB,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,gBAAgB,EAAE,WAAW,EAAE,cAAc,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,aAAU,AAAD;IAClF,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,UAAO,AAAD;IAEvB,MAAM,yBAAyB,CAAC;QAC9B,IAAI,CAAC,MAAM,OAAO,EAAE;QAEpB,OAAO,MAAM,MAAM,CAAC,CAAA;YAClB,IAAI,CAAC,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,QAAQ,CAAC,KAAK,IAAI,GAAG;gBACjD,IAAI,KAAK,QAAQ,EAAE;oBACjB,KAAK,QAAQ,GAAG,uBAAuB,KAAK,QAAQ;gBACtD;gBACA,OAAO;YACT;YACA,OAAO;QACT;IACF;IAEA,MAAM,qBAAqB,uBAAuB;IAClD,MAAM,2BAA2B,uBAAuB;IAExD,MAAM,WAAW,CAAC;QAChB,IAAI,SAAS,cAAc;YACzB,OAAO,aAAa,gBAAgB,aAAa;QACnD;QACA,OAAO,UAAU,WAAW;IAC9B;IAEA,MAAM,UAA8D,CAAC,EACnE,IAAI,EACJ,QAAQ,CAAC,EACV;QACC,MAAM,SAAS,SAAS,KAAK,IAAI;QACjC,MAAM,cAAc,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG;QAC5D,MAAM,iBAAiB,eAAe,KAAK,QAAQ,CAAC,IAAI,CAAC,CAAA,QAAS,SAAS,MAAM,IAAI;QAErF,qBACE,8OAAC;;8BACC,8OAAC,4JAAA,CAAA,UAAI;oBACH,MAAM,KAAK,IAAI;oBACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gIACA,QAAQ,KAAK,aACb,UAAU,iBACN,qCACA,yBACJ,oBAAoB,UAAU,KAAK;oBAErC,SAAS;wBACP,IAAI,OAAO,UAAU,GAAG,KAAK;4BAC3B,eAAe;wBACjB;oBACF;;sCAEA,8OAAC,KAAK,IAAI;4BACR,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oBACA,oBAAoB,UAAU,IAAI,SAAS;;;;;;wBAG9C,CAAC,CAAC,oBAAoB,QAAQ,CAAC,mBAC9B;;8CACE,8OAAC;oCAAK,WAAU;8CAAY,KAAK,IAAI;;;;;;gCACpC,KAAK,KAAK,kBACT,8OAAC,iIAAA,CAAA,QAAK;oCACJ,SAAQ;oCACR,WAAU;8CAET,KAAK,KAAK;;;;;;;;;;;;;;gBAQpB,eAAe,CAAC,CAAC,oBAAoB,QAAQ,CAAC,mBAC7C,8OAAC;oBAAI,WAAU;8BACZ,KAAK,QAAQ,CAAE,GAAG,CAAC,CAAC,sBACnB,8OAAC;4BAAyB,MAAM;4BAAO,OAAO,QAAQ;2BAAxC,MAAM,IAAI;;;;;;;;;;;;;;;;IAMpC;IAEA,qBACE;;0BAEE,8OAAC;gBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uIACA,mBAAmB,SAAS;;kCAI9B,8OAAC;wBAAI,WAAU;;4BACZ,CAAC,kCACA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,gNAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;kDAEzB,8OAAC;wCAAK,WAAU;kDAAwB;;;;;;;;;;;;0CAG5C,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;0CAET,iCACC,8OAAC,sNAAA,CAAA,eAAY;oCAAC,WAAU;;;;;yDAExB,8OAAC,oNAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAM7B,8OAAC;wBAAI,WAAU;kCACZ,mBAAmB,GAAG,CAAC,CAAC,qBACvB,8OAAC;gCAAwB,MAAM;+BAAjB,KAAK,IAAI;;;;;;;;;;kCAK3B,8OAAC;wBAAI,WAAU;kCACZ,yBAAyB,GAAG,CAAC,CAAC,qBAC7B,8OAAC;gCAAwB,MAAM;+BAAjB,KAAK,IAAI;;;;;;;;;;;;;;;;0BAM7B,8OAAC;gBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kJACA,cAAc,kBAAkB;;kCAIlC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,gNAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;8CAEzB,8OAAC;oCAAK,WAAU;8CAAwB;;;;;;;;;;;;;;;;;kCAK5C,8OAAC;wBAAI,WAAU;kCACZ,mBAAmB,GAAG,CAAC,CAAC,qBACvB,8OAAC;gCAAwB,MAAM;+BAAjB,KAAK,IAAI;;;;;;;;;;kCAK3B,8OAAC;wBAAI,WAAU;kCACZ,yBAAyB,GAAG,CAAC,CAAC,qBAC7B,8OAAC;gCAAwB,MAAM;+BAAjB,KAAK,IAAI;;;;;;;;;;;;;;;;;;AAMnC;uCAEe", "debugId": null}}, {"offset": {"line": 1947, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/test/dentist-appointment-platform/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,8OAAC,4KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,8OAAC,4KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,8OAAC,4KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,8OAAC,4KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2209, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/test/dentist-appointment-platform/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,kKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2261, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/test/dentist-appointment-platform/src/components/layout/Header.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { useRouter } from 'next/navigation';\nimport { cn, stringUtils } from '@/lib/utils';\nimport { useSidebar, usePageState, useNotifications } from '@/store/uiStore';\nimport { useAuth, useAuthActions } from '@/store/authStore';\nimport { Button } from '@/components/ui/button';\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu';\nimport { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';\nimport { Badge } from '@/components/ui/badge';\nimport {\n  Menu,\n  Bell,\n  Search,\n  Settings,\n  User,\n  LogOut,\n  Moon,\n  Sun,\n  Monitor,\n  HelpCircle,\n} from 'lucide-react';\n\nconst Header: React.FC = () => {\n  const router = useRouter();\n  const { toggleSidebar, setSidebarOpen } = useSidebar();\n  const { pageTitle } = usePageState();\n  const { notifications, clearNotifications } = useNotifications();\n  const { user } = useAuth();\n  const { logout } = useAuthActions();\n\n  const unreadNotifications = notifications.filter(n => !n.action);\n\n  const handleLogout = async () => {\n    await logout();\n    router.push('/');\n  };\n\n  const handleProfileClick = () => {\n    router.push('/profile');\n  };\n\n  const handleSettingsClick = () => {\n    router.push('/settings');\n  };\n\n  const handleNotificationClick = () => {\n    // Open notifications panel or navigate to notifications page\n    router.push('/notifications');\n  };\n\n  return (\n    <header className=\"sticky top-0 z-30 border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n      <div className=\"flex h-16 items-center justify-between px-4\">\n        {/* Left side - Menu toggle and page title */}\n        <div className=\"flex items-center space-x-4\">\n          {/* Mobile menu toggle */}\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            className=\"md:hidden\"\n            onClick={() => setSidebarOpen(true)}\n          >\n            <Menu className=\"h-5 w-5\" />\n            <span className=\"sr-only\">Toggle sidebar</span>\n          </Button>\n\n          {/* Desktop sidebar toggle */}\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            className=\"hidden md:flex\"\n            onClick={toggleSidebar}\n          >\n            <Menu className=\"h-5 w-5\" />\n            <span className=\"sr-only\">Toggle sidebar</span>\n          </Button>\n\n          {/* Page title */}\n          <div className=\"hidden sm:block\">\n            <h1 className=\"text-lg font-semibold text-foreground\">\n              {pageTitle}\n            </h1>\n          </div>\n        </div>\n\n        {/* Center - Search (optional, can be added later) */}\n        <div className=\"hidden lg:flex flex-1 max-w-md mx-8\">\n          <div className=\"relative w-full\">\n            <Search className=\"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground\" />\n            <input\n              type=\"search\"\n              placeholder=\"Search...\"\n              className=\"w-full rounded-md border border-input bg-background pl-10 pr-4 py-2 text-sm placeholder:text-muted-foreground focus:border-primary focus:outline-none focus:ring-2 focus:ring-primary/20\"\n            />\n          </div>\n        </div>\n\n        {/* Right side - Notifications and user menu */}\n        <div className=\"flex items-center space-x-2\">\n          {/* Search button for mobile */}\n          <Button variant=\"ghost\" size=\"icon\" className=\"lg:hidden\">\n            <Search className=\"h-5 w-5\" />\n            <span className=\"sr-only\">Search</span>\n          </Button>\n\n          {/* Notifications */}\n          <DropdownMenu>\n            <DropdownMenuTrigger asChild>\n              <Button variant=\"ghost\" size=\"icon\" className=\"relative\">\n                <Bell className=\"h-5 w-5\" />\n                {unreadNotifications.length > 0 && (\n                  <Badge\n                    variant=\"destructive\"\n                    className=\"absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 text-xs\"\n                  >\n                    {unreadNotifications.length > 9 ? '9+' : unreadNotifications.length}\n                  </Badge>\n                )}\n                <span className=\"sr-only\">Notifications</span>\n              </Button>\n            </DropdownMenuTrigger>\n            <DropdownMenuContent align=\"end\" className=\"w-80\">\n              <DropdownMenuLabel className=\"flex items-center justify-between\">\n                Notifications\n                {notifications.length > 0 && (\n                  <Button\n                    variant=\"ghost\"\n                    size=\"sm\"\n                    onClick={clearNotifications}\n                    className=\"h-auto p-0 text-xs text-muted-foreground hover:text-foreground\"\n                  >\n                    Clear all\n                  </Button>\n                )}\n              </DropdownMenuLabel>\n              <DropdownMenuSeparator />\n              {notifications.length === 0 ? (\n                <div className=\"p-4 text-center text-sm text-muted-foreground\">\n                  No notifications\n                </div>\n              ) : (\n                <div className=\"max-h-64 overflow-y-auto\">\n                  {notifications.slice(0, 5).map((notification) => (\n                    <DropdownMenuItem\n                      key={notification.id}\n                      className=\"flex flex-col items-start p-3 cursor-pointer\"\n                      onClick={handleNotificationClick}\n                    >\n                      <div className=\"flex w-full items-start justify-between\">\n                        <div className=\"flex-1\">\n                          <p className=\"text-sm font-medium\">{notification.title}</p>\n                          {notification.message && (\n                            <p className=\"text-xs text-muted-foreground mt-1\">\n                              {stringUtils.truncate(notification.message, 60)}\n                            </p>\n                          )}\n                        </div>\n                        <Badge\n                          variant={\n                            notification.type === 'error'\n                              ? 'destructive'\n                              : notification.type === 'warning'\n                              ? 'secondary'\n                              : 'default'\n                          }\n                          className=\"ml-2 text-xs\"\n                        >\n                          {notification.type}\n                        </Badge>\n                      </div>\n                    </DropdownMenuItem>\n                  ))}\n                  {notifications.length > 5 && (\n                    <DropdownMenuItem\n                      className=\"text-center text-sm text-primary\"\n                      onClick={handleNotificationClick}\n                    >\n                      View all notifications\n                    </DropdownMenuItem>\n                  )}\n                </div>\n              )}\n            </DropdownMenuContent>\n          </DropdownMenu>\n\n          {/* User menu */}\n          <DropdownMenu>\n            <DropdownMenuTrigger asChild>\n              <Button variant=\"ghost\" className=\"relative h-10 w-10 rounded-full\">\n                <Avatar className=\"h-10 w-10\">\n                  <AvatarImage src={user?.image} alt={user?.name || 'User'} />\n                  <AvatarFallback className=\"bg-primary/10 text-primary\">\n                    {user?.name ? stringUtils.initials(user.name) : 'U'}\n                  </AvatarFallback>\n                </Avatar>\n              </Button>\n            </DropdownMenuTrigger>\n            <DropdownMenuContent className=\"w-56\" align=\"end\" forceMount>\n              <DropdownMenuLabel className=\"font-normal\">\n                <div className=\"flex flex-col space-y-1\">\n                  <p className=\"text-sm font-medium leading-none\">\n                    {user?.name || 'User'}\n                  </p>\n                  <p className=\"text-xs leading-none text-muted-foreground\">\n                    {user?.email}\n                  </p>\n                  <Badge variant=\"outline\" className=\"w-fit text-xs mt-1\">\n                    {stringUtils.capitalizeWords(user?.role.toLowerCase() || '')}\n                  </Badge>\n                </div>\n              </DropdownMenuLabel>\n              <DropdownMenuSeparator />\n              \n              <DropdownMenuItem onClick={handleProfileClick}>\n                <User className=\"mr-2 h-4 w-4\" />\n                <span>Profile</span>\n              </DropdownMenuItem>\n              \n              <DropdownMenuItem onClick={handleSettingsClick}>\n                <Settings className=\"mr-2 h-4 w-4\" />\n                <span>Settings</span>\n              </DropdownMenuItem>\n\n              <DropdownMenuItem onClick={() => router.push('/help')}>\n                <HelpCircle className=\"mr-2 h-4 w-4\" />\n                <span>Help & Support</span>\n              </DropdownMenuItem>\n\n              <DropdownMenuSeparator />\n\n              {/* Theme toggle */}\n              <DropdownMenuLabel className=\"text-xs text-muted-foreground\">\n                Theme\n              </DropdownMenuLabel>\n              <DropdownMenuItem>\n                <Sun className=\"mr-2 h-4 w-4\" />\n                <span>Light</span>\n              </DropdownMenuItem>\n              <DropdownMenuItem>\n                <Moon className=\"mr-2 h-4 w-4\" />\n                <span>Dark</span>\n              </DropdownMenuItem>\n              <DropdownMenuItem>\n                <Monitor className=\"mr-2 h-4 w-4\" />\n                <span>System</span>\n              </DropdownMenuItem>\n\n              <DropdownMenuSeparator />\n              \n              <DropdownMenuItem onClick={handleLogout} className=\"text-destructive\">\n                <LogOut className=\"mr-2 h-4 w-4\" />\n                <span>Log out</span>\n              </DropdownMenuItem>\n            </DropdownMenuContent>\n          </DropdownMenu>\n        </div>\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AAQA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAlBA;;;;;;;;;;;AA+BA,MAAM,SAAmB;IACvB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,aAAa,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,aAAU,AAAD;IACnD,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,eAAY,AAAD;IACjC,MAAM,EAAE,aAAa,EAAE,kBAAkB,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,mBAAgB,AAAD;IAC7D,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,iBAAc,AAAD;IAEhC,MAAM,sBAAsB,cAAc,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,MAAM;IAE/D,MAAM,eAAe;QACnB,MAAM;QACN,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,qBAAqB;QACzB,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,sBAAsB;QAC1B,OAAO,IAAI,CAAC;IACd;IAEA,MAAM,0BAA0B;QAC9B,6DAA6D;QAC7D,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,eAAe;;8CAE9B,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;;sCAI5B,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,SAAS;;8CAET,8OAAC,kMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;8CAChB,8OAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;;sCAI5B,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAG,WAAU;0CACX;;;;;;;;;;;;;;;;;8BAMP,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,WAAU;;;;;;;;;;;;;;;;;8BAMhB,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAQ,MAAK;4BAAO,WAAU;;8CAC5C,8OAAC,sMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,8OAAC;oCAAK,WAAU;8CAAU;;;;;;;;;;;;sCAI5B,8OAAC,4IAAA,CAAA,eAAY;;8CACX,8OAAC,4IAAA,CAAA,sBAAmB;oCAAC,OAAO;8CAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,MAAK;wCAAO,WAAU;;0DAC5C,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CACf,oBAAoB,MAAM,GAAG,mBAC5B,8OAAC,iIAAA,CAAA,QAAK;gDACJ,SAAQ;gDACR,WAAU;0DAET,oBAAoB,MAAM,GAAG,IAAI,OAAO,oBAAoB,MAAM;;;;;;0DAGvE,8OAAC;gDAAK,WAAU;0DAAU;;;;;;;;;;;;;;;;;8CAG9B,8OAAC,4IAAA,CAAA,sBAAmB;oCAAC,OAAM;oCAAM,WAAU;;sDACzC,8OAAC,4IAAA,CAAA,oBAAiB;4CAAC,WAAU;;gDAAoC;gDAE9D,cAAc,MAAM,GAAG,mBACtB,8OAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;oDACT,WAAU;8DACX;;;;;;;;;;;;sDAKL,8OAAC,4IAAA,CAAA,wBAAqB;;;;;wCACrB,cAAc,MAAM,KAAK,kBACxB,8OAAC;4CAAI,WAAU;sDAAgD;;;;;iEAI/D,8OAAC;4CAAI,WAAU;;gDACZ,cAAc,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,6BAC9B,8OAAC,4IAAA,CAAA,mBAAgB;wDAEf,WAAU;wDACV,SAAS;kEAET,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAE,WAAU;sFAAuB,aAAa,KAAK;;;;;;wEACrD,aAAa,OAAO,kBACnB,8OAAC;4EAAE,WAAU;sFACV,mHAAA,CAAA,cAAW,CAAC,QAAQ,CAAC,aAAa,OAAO,EAAE;;;;;;;;;;;;8EAIlD,8OAAC,iIAAA,CAAA,QAAK;oEACJ,SACE,aAAa,IAAI,KAAK,UAClB,gBACA,aAAa,IAAI,KAAK,YACtB,cACA;oEAEN,WAAU;8EAET,aAAa,IAAI;;;;;;;;;;;;uDAvBjB,aAAa,EAAE;;;;;gDA4BvB,cAAc,MAAM,GAAG,mBACtB,8OAAC,4IAAA,CAAA,mBAAgB;oDACf,WAAU;oDACV,SAAS;8DACV;;;;;;;;;;;;;;;;;;;;;;;;sCAUX,8OAAC,4IAAA,CAAA,eAAY;;8CACX,8OAAC,4IAAA,CAAA,sBAAmB;oCAAC,OAAO;8CAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,WAAU;kDAChC,cAAA,8OAAC,kIAAA,CAAA,SAAM;4CAAC,WAAU;;8DAChB,8OAAC,kIAAA,CAAA,cAAW;oDAAC,KAAK,MAAM;oDAAO,KAAK,MAAM,QAAQ;;;;;;8DAClD,8OAAC,kIAAA,CAAA,iBAAc;oDAAC,WAAU;8DACvB,MAAM,OAAO,mHAAA,CAAA,cAAW,CAAC,QAAQ,CAAC,KAAK,IAAI,IAAI;;;;;;;;;;;;;;;;;;;;;;8CAKxD,8OAAC,4IAAA,CAAA,sBAAmB;oCAAC,WAAU;oCAAO,OAAM;oCAAM,UAAU;;sDAC1D,8OAAC,4IAAA,CAAA,oBAAiB;4CAAC,WAAU;sDAC3B,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAE,WAAU;kEACV,MAAM,QAAQ;;;;;;kEAEjB,8OAAC;wDAAE,WAAU;kEACV,MAAM;;;;;;kEAET,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;wDAAU,WAAU;kEAChC,mHAAA,CAAA,cAAW,CAAC,eAAe,CAAC,MAAM,KAAK,iBAAiB;;;;;;;;;;;;;;;;;sDAI/D,8OAAC,4IAAA,CAAA,wBAAqB;;;;;sDAEtB,8OAAC,4IAAA,CAAA,mBAAgB;4CAAC,SAAS;;8DACzB,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;8DAAK;;;;;;;;;;;;sDAGR,8OAAC,4IAAA,CAAA,mBAAgB;4CAAC,SAAS;;8DACzB,8OAAC,0MAAA,CAAA,WAAQ;oDAAC,WAAU;;;;;;8DACpB,8OAAC;8DAAK;;;;;;;;;;;;sDAGR,8OAAC,4IAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,OAAO,IAAI,CAAC;;8DAC3C,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;8DACtB,8OAAC;8DAAK;;;;;;;;;;;;sDAGR,8OAAC,4IAAA,CAAA,wBAAqB;;;;;sDAGtB,8OAAC,4IAAA,CAAA,oBAAiB;4CAAC,WAAU;sDAAgC;;;;;;sDAG7D,8OAAC,4IAAA,CAAA,mBAAgB;;8DACf,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;8DACf,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC,4IAAA,CAAA,mBAAgB;;8DACf,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;8DAAK;;;;;;;;;;;;sDAER,8OAAC,4IAAA,CAAA,mBAAgB;;8DACf,8OAAC,wMAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;8DACnB,8OAAC;8DAAK;;;;;;;;;;;;sDAGR,8OAAC,4IAAA,CAAA,wBAAqB;;;;;sDAEtB,8OAAC,4IAAA,CAAA,mBAAgB;4CAAC,SAAS;4CAAc,WAAU;;8DACjD,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQtB;uCAEe", "debugId": null}}, {"offset": {"line": 2929, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/test/dentist-appointment-platform/src/components/layout/Breadcrumbs.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport Link from 'next/link';\nimport { useRouter } from 'next/navigation';\nimport { cn } from '@/lib/utils';\nimport { ChevronRight, Home } from 'lucide-react';\n\nexport interface BreadcrumbItem {\n  label: string;\n  href?: string;\n  icon?: React.ComponentType<{ className?: string }>;\n  current?: boolean;\n}\n\ninterface BreadcrumbsProps {\n  items: BreadcrumbItem[];\n  className?: string;\n  separator?: React.ReactNode;\n  showHome?: boolean;\n  maxItems?: number;\n}\n\nconst Breadcrumbs: React.FC<BreadcrumbsProps> = ({\n  items,\n  className,\n  separator = <ChevronRight className=\"h-4 w-4 text-muted-foreground\" />,\n  showHome = true,\n  maxItems = 5,\n}) => {\n  const router = useRouter();\n\n  // Add home item if showHome is true and not already present\n  const allItems = showHome && items[0]?.label !== 'Home' \n    ? [{ label: 'Home', href: '/dashboard', icon: Home }, ...items]\n    : items;\n\n  // Truncate items if there are too many\n  const displayItems = allItems.length > maxItems\n    ? [\n        allItems[0],\n        { label: '...', href: undefined },\n        ...allItems.slice(-maxItems + 2)\n      ]\n    : allItems;\n\n  const handleClick = (item: BreadcrumbItem, index: number) => {\n    if (item.href && !item.current) {\n      router.push(item.href);\n    }\n  };\n\n  return (\n    <nav\n      aria-label=\"Breadcrumb\"\n      className={cn('flex items-center space-x-1 text-sm', className)}\n    >\n      <ol className=\"flex items-center space-x-1\">\n        {displayItems.map((item, index) => {\n          const isLast = index === displayItems.length - 1;\n          const isCurrent = item.current || isLast;\n          const isEllipsis = item.label === '...';\n\n          return (\n            <li key={`${item.label}-${index}`} className=\"flex items-center\">\n              {index > 0 && (\n                <span className=\"mx-2 flex-shrink-0\">\n                  {separator}\n                </span>\n              )}\n\n              {isEllipsis ? (\n                <span className=\"text-muted-foreground px-2\">...</span>\n              ) : (\n                <div className=\"flex items-center\">\n                  {item.href && !isCurrent ? (\n                    <Link\n                      href={item.href}\n                      className={cn(\n                        'flex items-center space-x-1 rounded-md px-2 py-1 transition-colors hover:bg-accent hover:text-accent-foreground',\n                        'text-muted-foreground hover:text-foreground'\n                      )}\n                      onClick={(e) => {\n                        e.preventDefault();\n                        handleClick(item, index);\n                      }}\n                    >\n                      {item.icon && (\n                        <item.icon className=\"h-4 w-4 flex-shrink-0\" />\n                      )}\n                      <span className=\"truncate max-w-[150px] sm:max-w-[200px]\">\n                        {item.label}\n                      </span>\n                    </Link>\n                  ) : (\n                    <span\n                      className={cn(\n                        'flex items-center space-x-1 px-2 py-1 rounded-md',\n                        isCurrent\n                          ? 'text-foreground font-medium bg-accent/50'\n                          : 'text-muted-foreground'\n                      )}\n                      aria-current={isCurrent ? 'page' : undefined}\n                    >\n                      {item.icon && (\n                        <item.icon className=\"h-4 w-4 flex-shrink-0\" />\n                      )}\n                      <span className=\"truncate max-w-[150px] sm:max-w-[200px]\">\n                        {item.label}\n                      </span>\n                    </span>\n                  )}\n                </div>\n              )}\n            </li>\n          );\n        })}\n      </ol>\n    </nav>\n  );\n};\n\n// Hook to generate breadcrumbs from pathname\nexport const useBreadcrumbs = (customItems?: BreadcrumbItem[]) => {\n  const router = useRouter();\n  \n  const generateBreadcrumbs = (pathname: string): BreadcrumbItem[] => {\n    if (customItems) return customItems;\n\n    const segments = pathname.split('/').filter(Boolean);\n    const breadcrumbs: BreadcrumbItem[] = [];\n\n    let currentPath = '';\n    \n    segments.forEach((segment, index) => {\n      currentPath += `/${segment}`;\n      const isLast = index === segments.length - 1;\n      \n      // Convert segment to readable label\n      const label = segment\n        .split('-')\n        .map(word => word.charAt(0).toUpperCase() + word.slice(1))\n        .join(' ');\n\n      breadcrumbs.push({\n        label,\n        href: isLast ? undefined : currentPath,\n        current: isLast,\n      });\n    });\n\n    return breadcrumbs;\n  };\n\n  return { generateBreadcrumbs };\n};\n\n// Predefined breadcrumb configurations for common pages\nexport const breadcrumbConfigs: Record<string, BreadcrumbItem[]> = {\n  '/dashboard': [\n    { label: 'Dashboard', current: true }\n  ],\n  '/appointments': [\n    { label: 'Appointments', current: true }\n  ],\n  '/appointments/book': [\n    { label: 'Appointments', href: '/appointments' },\n    { label: 'Book Appointment', current: true }\n  ],\n  '/appointments/schedule': [\n    { label: 'Appointments', href: '/appointments' },\n    { label: 'Schedule', current: true }\n  ],\n  '/patients': [\n    { label: 'Patients', current: true }\n  ],\n  '/patients/add': [\n    { label: 'Patients', href: '/patients' },\n    { label: 'Add Patient', current: true }\n  ],\n  '/documents': [\n    { label: 'Documents', current: true }\n  ],\n  '/documents/upload': [\n    { label: 'Documents', href: '/documents' },\n    { label: 'Upload Document', current: true }\n  ],\n  '/profile': [\n    { label: 'Profile', current: true }\n  ],\n  '/settings': [\n    { label: 'Settings', current: true }\n  ],\n  '/practice': [\n    { label: 'Practice Management', current: true }\n  ],\n  '/practice/hours': [\n    { label: 'Practice Management', href: '/practice' },\n    { label: 'Working Hours', current: true }\n  ],\n  '/practice/services': [\n    { label: 'Practice Management', href: '/practice' },\n    { label: 'Services', current: true }\n  ],\n  '/practice/staff': [\n    { label: 'Practice Management', href: '/practice' },\n    { label: 'Staff', current: true }\n  ],\n  '/admin': [\n    { label: 'Administration', current: true }\n  ],\n  '/admin/users': [\n    { label: 'Administration', href: '/admin' },\n    { label: 'User Management', current: true }\n  ],\n  '/admin/settings': [\n    { label: 'Administration', href: '/admin' },\n    { label: 'System Settings', current: true }\n  ],\n  '/admin/audit': [\n    { label: 'Administration', href: '/admin' },\n    { label: 'Audit Logs', current: true }\n  ],\n};\n\n// Component for automatic breadcrumb generation\nexport const AutoBreadcrumbs: React.FC<{\n  pathname: string;\n  className?: string;\n}> = ({ pathname, className }) => {\n  const { generateBreadcrumbs } = useBreadcrumbs();\n  \n  // Use predefined config if available, otherwise generate from pathname\n  const items = breadcrumbConfigs[pathname] || generateBreadcrumbs(pathname);\n  \n  if (items.length === 0) return null;\n  \n  return <Breadcrumbs items={items} className={className} />;\n};\n\nexport default Breadcrumbs;\n"], "names": [], "mappings": ";;;;;;;AAGA;AACA;AACA;AACA;AAAA;AANA;;;;;;AAuBA,MAAM,cAA0C,CAAC,EAC/C,KAAK,EACL,SAAS,EACT,0BAAY,8OAAC,sNAAA,CAAA,eAAY;IAAC,WAAU;;;;;QAAkC,EACtE,WAAW,IAAI,EACf,WAAW,CAAC,EACb;IACC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,4DAA4D;IAC5D,MAAM,WAAW,YAAY,KAAK,CAAC,EAAE,EAAE,UAAU,SAC7C;QAAC;YAAE,OAAO;YAAQ,MAAM;YAAc,MAAM,mMAAA,CAAA,OAAI;QAAC;WAAM;KAAM,GAC7D;IAEJ,uCAAuC;IACvC,MAAM,eAAe,SAAS,MAAM,GAAG,WACnC;QACE,QAAQ,CAAC,EAAE;QACX;YAAE,OAAO;YAAO,MAAM;QAAU;WAC7B,SAAS,KAAK,CAAC,CAAC,WAAW;KAC/B,GACD;IAEJ,MAAM,cAAc,CAAC,MAAsB;QACzC,IAAI,KAAK,IAAI,IAAI,CAAC,KAAK,OAAO,EAAE;YAC9B,OAAO,IAAI,CAAC,KAAK,IAAI;QACvB;IACF;IAEA,qBACE,8OAAC;QACC,cAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uCAAuC;kBAErD,cAAA,8OAAC;YAAG,WAAU;sBACX,aAAa,GAAG,CAAC,CAAC,MAAM;gBACvB,MAAM,SAAS,UAAU,aAAa,MAAM,GAAG;gBAC/C,MAAM,YAAY,KAAK,OAAO,IAAI;gBAClC,MAAM,aAAa,KAAK,KAAK,KAAK;gBAElC,qBACE,8OAAC;oBAAkC,WAAU;;wBAC1C,QAAQ,mBACP,8OAAC;4BAAK,WAAU;sCACb;;;;;;wBAIJ,2BACC,8OAAC;4BAAK,WAAU;sCAA6B;;;;;iDAE7C,8OAAC;4BAAI,WAAU;sCACZ,KAAK,IAAI,IAAI,CAAC,0BACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAM,KAAK,IAAI;gCACf,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,mHACA;gCAEF,SAAS,CAAC;oCACR,EAAE,cAAc;oCAChB,YAAY,MAAM;gCACpB;;oCAEC,KAAK,IAAI,kBACR,8OAAC,KAAK,IAAI;wCAAC,WAAU;;;;;;kDAEvB,8OAAC;wCAAK,WAAU;kDACb,KAAK,KAAK;;;;;;;;;;;qDAIf,8OAAC;gCACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oDACA,YACI,6CACA;gCAEN,gBAAc,YAAY,SAAS;;oCAElC,KAAK,IAAI,kBACR,8OAAC,KAAK,IAAI;wCAAC,WAAU;;;;;;kDAEvB,8OAAC;wCAAK,WAAU;kDACb,KAAK,KAAK;;;;;;;;;;;;;;;;;;mBA5Cd,GAAG,KAAK,KAAK,CAAC,CAAC,EAAE,OAAO;;;;;YAoDrC;;;;;;;;;;;AAIR;AAGO,MAAM,iBAAiB,CAAC;IAC7B,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,sBAAsB,CAAC;QAC3B,IAAI,aAAa,OAAO;QAExB,MAAM,WAAW,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;QAC5C,MAAM,cAAgC,EAAE;QAExC,IAAI,cAAc;QAElB,SAAS,OAAO,CAAC,CAAC,SAAS;YACzB,eAAe,CAAC,CAAC,EAAE,SAAS;YAC5B,MAAM,SAAS,UAAU,SAAS,MAAM,GAAG;YAE3C,oCAAoC;YACpC,MAAM,QAAQ,QACX,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,IACtD,IAAI,CAAC;YAER,YAAY,IAAI,CAAC;gBACf;gBACA,MAAM,SAAS,YAAY;gBAC3B,SAAS;YACX;QACF;QAEA,OAAO;IACT;IAEA,OAAO;QAAE;IAAoB;AAC/B;AAGO,MAAM,oBAAsD;IACjE,cAAc;QACZ;YAAE,OAAO;YAAa,SAAS;QAAK;KACrC;IACD,iBAAiB;QACf;YAAE,OAAO;YAAgB,SAAS;QAAK;KACxC;IACD,sBAAsB;QACpB;YAAE,OAAO;YAAgB,MAAM;QAAgB;QAC/C;YAAE,OAAO;YAAoB,SAAS;QAAK;KAC5C;IACD,0BAA0B;QACxB;YAAE,OAAO;YAAgB,MAAM;QAAgB;QAC/C;YAAE,OAAO;YAAY,SAAS;QAAK;KACpC;IACD,aAAa;QACX;YAAE,OAAO;YAAY,SAAS;QAAK;KACpC;IACD,iBAAiB;QACf;YAAE,OAAO;YAAY,MAAM;QAAY;QACvC;YAAE,OAAO;YAAe,SAAS;QAAK;KACvC;IACD,cAAc;QACZ;YAAE,OAAO;YAAa,SAAS;QAAK;KACrC;IACD,qBAAqB;QACnB;YAAE,OAAO;YAAa,MAAM;QAAa;QACzC;YAAE,OAAO;YAAmB,SAAS;QAAK;KAC3C;IACD,YAAY;QACV;YAAE,OAAO;YAAW,SAAS;QAAK;KACnC;IACD,aAAa;QACX;YAAE,OAAO;YAAY,SAAS;QAAK;KACpC;IACD,aAAa;QACX;YAAE,OAAO;YAAuB,SAAS;QAAK;KAC/C;IACD,mBAAmB;QACjB;YAAE,OAAO;YAAuB,MAAM;QAAY;QAClD;YAAE,OAAO;YAAiB,SAAS;QAAK;KACzC;IACD,sBAAsB;QACpB;YAAE,OAAO;YAAuB,MAAM;QAAY;QAClD;YAAE,OAAO;YAAY,SAAS;QAAK;KACpC;IACD,mBAAmB;QACjB;YAAE,OAAO;YAAuB,MAAM;QAAY;QAClD;YAAE,OAAO;YAAS,SAAS;QAAK;KACjC;IACD,UAAU;QACR;YAAE,OAAO;YAAkB,SAAS;QAAK;KAC1C;IACD,gBAAgB;QACd;YAAE,OAAO;YAAkB,MAAM;QAAS;QAC1C;YAAE,OAAO;YAAmB,SAAS;QAAK;KAC3C;IACD,mBAAmB;QACjB;YAAE,OAAO;YAAkB,MAAM;QAAS;QAC1C;YAAE,OAAO;YAAmB,SAAS;QAAK;KAC3C;IACD,gBAAgB;QACd;YAAE,OAAO;YAAkB,MAAM;QAAS;QAC1C;YAAE,OAAO;YAAc,SAAS;QAAK;KACtC;AACH;AAGO,MAAM,kBAGR,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE;IAC3B,MAAM,EAAE,mBAAmB,EAAE,GAAG;IAEhC,uEAAuE;IACvE,MAAM,QAAQ,iBAAiB,CAAC,SAAS,IAAI,oBAAoB;IAEjE,IAAI,MAAM,MAAM,KAAK,GAAG,OAAO;IAE/B,qBAAO,8OAAC;QAAY,OAAO;QAAO,WAAW;;;;;;AAC/C;uCAEe", "debugId": null}}, {"offset": {"line": 3278, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/test/dentist-appointment-platform/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 3375, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/test/dentist-appointment-platform/src/components/common/ErrorBoundary.tsx"], "sourcesContent": ["'use client';\n\nimport React, { Component, ErrorInfo, ReactNode } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';\nimport { <PERSON><PERSON><PERSON><PERSON>gle, RefreshCw, Home, Bug } from 'lucide-react';\n\ninterface Props {\n  children: ReactNode;\n  fallback?: ReactNode;\n  onError?: (error: Error, errorInfo: ErrorInfo) => void;\n  showDetails?: boolean;\n  level?: 'page' | 'component' | 'critical';\n}\n\ninterface State {\n  hasError: boolean;\n  error: Error | null;\n  errorInfo: ErrorInfo | null;\n  errorId: string | null;\n}\n\nclass ErrorBoundary extends Component<Props, State> {\n  constructor(props: Props) {\n    super(props);\n    this.state = {\n      hasError: false,\n      error: null,\n      errorInfo: null,\n      errorId: null,\n    };\n  }\n\n  static getDerivedStateFromError(error: Error): Partial<State> {\n    // Update state so the next render will show the fallback UI\n    return {\n      hasError: true,\n      error,\n      errorId: `error_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,\n    };\n  }\n\n  componentDidCatch(error: Error, errorInfo: ErrorInfo) {\n    // Log error details\n    console.error('ErrorBoundary caught an error:', error, errorInfo);\n\n    this.setState({\n      error,\n      errorInfo,\n    });\n\n    // Call custom error handler if provided\n    if (this.props.onError) {\n      this.props.onError(error, errorInfo);\n    }\n\n    // In production, you might want to send this to an error reporting service\n    if (process.env.NODE_ENV === 'production') {\n      this.logErrorToService(error, errorInfo);\n    }\n  }\n\n  private logErrorToService = (error: Error, errorInfo: ErrorInfo) => {\n    // This would typically send to a service like Sentry, LogRocket, etc.\n    const errorData = {\n      message: error.message,\n      stack: error.stack,\n      componentStack: errorInfo.componentStack,\n      errorId: this.state.errorId,\n      timestamp: new Date().toISOString(),\n      userAgent: navigator.userAgent,\n      url: window.location.href,\n    };\n\n    // Example: Send to error reporting service\n    // errorReportingService.captureException(errorData);\n    console.log('Error logged:', errorData);\n  };\n\n  private handleRetry = () => {\n    this.setState({\n      hasError: false,\n      error: null,\n      errorInfo: null,\n      errorId: null,\n    });\n  };\n\n  private handleReload = () => {\n    window.location.reload();\n  };\n\n  private handleGoHome = () => {\n    window.location.href = '/';\n  };\n\n  private copyErrorDetails = () => {\n    const { error, errorInfo, errorId } = this.state;\n    const errorDetails = {\n      errorId,\n      message: error?.message,\n      stack: error?.stack,\n      componentStack: errorInfo?.componentStack,\n      timestamp: new Date().toISOString(),\n      url: window.location.href,\n    };\n\n    navigator.clipboard.writeText(JSON.stringify(errorDetails, null, 2));\n  };\n\n  render() {\n    if (this.state.hasError) {\n      // Custom fallback UI\n      if (this.props.fallback) {\n        return this.props.fallback;\n      }\n\n      const { error, errorInfo, errorId } = this.state;\n      const { level = 'component', showDetails = false } = this.props;\n\n      // Critical error - full page\n      if (level === 'critical') {\n        return (\n          <div className=\"min-h-screen flex items-center justify-center bg-background p-4\">\n            <Card className=\"w-full max-w-2xl glass-card\">\n              <CardHeader className=\"text-center\">\n                <div className=\"mx-auto mb-4 flex h-16 w-16 items-center justify-center rounded-full bg-destructive/10\">\n                  <AlertTriangle className=\"h-8 w-8 text-destructive\" />\n                </div>\n                <CardTitle className=\"text-2xl\">Critical Error</CardTitle>\n                <CardDescription>\n                  A critical error has occurred that prevents the application from functioning properly.\n                </CardDescription>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                {showDetails && error && (\n                  <div className=\"rounded-md bg-muted p-4\">\n                    <h4 className=\"font-medium mb-2\">Error Details:</h4>\n                    <p className=\"text-sm text-muted-foreground font-mono\">\n                      {error.message}\n                    </p>\n                    {errorId && (\n                      <p className=\"text-xs text-muted-foreground mt-2\">\n                        Error ID: {errorId}\n                      </p>\n                    )}\n                  </div>\n                )}\n                <div className=\"flex flex-col sm:flex-row gap-3\">\n                  <Button onClick={this.handleReload} className=\"flex-1\">\n                    <RefreshCw className=\"mr-2 h-4 w-4\" />\n                    Reload Page\n                  </Button>\n                  <Button variant=\"outline\" onClick={this.handleGoHome} className=\"flex-1\">\n                    <Home className=\"mr-2 h-4 w-4\" />\n                    Go Home\n                  </Button>\n                  {showDetails && (\n                    <Button variant=\"outline\" onClick={this.copyErrorDetails}>\n                      <Bug className=\"mr-2 h-4 w-4\" />\n                      Copy Details\n                    </Button>\n                  )}\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n        );\n      }\n\n      // Page-level error\n      if (level === 'page') {\n        return (\n          <div className=\"flex min-h-[400px] items-center justify-center p-4\">\n            <Card className=\"w-full max-w-lg glass-card\">\n              <CardHeader className=\"text-center\">\n                <div className=\"mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-destructive/10\">\n                  <AlertTriangle className=\"h-6 w-6 text-destructive\" />\n                </div>\n                <CardTitle>Something went wrong</CardTitle>\n                <CardDescription>\n                  We encountered an error while loading this page.\n                </CardDescription>\n              </CardHeader>\n              <CardContent className=\"space-y-4\">\n                {showDetails && error && (\n                  <div className=\"rounded-md bg-muted p-3\">\n                    <p className=\"text-sm text-muted-foreground font-mono\">\n                      {error.message}\n                    </p>\n                    {errorId && (\n                      <p className=\"text-xs text-muted-foreground mt-1\">\n                        Error ID: {errorId}\n                      </p>\n                    )}\n                  </div>\n                )}\n                <div className=\"flex gap-3\">\n                  <Button onClick={this.handleRetry} className=\"flex-1\">\n                    <RefreshCw className=\"mr-2 h-4 w-4\" />\n                    Try Again\n                  </Button>\n                  <Button variant=\"outline\" onClick={this.handleGoHome}>\n                    <Home className=\"mr-2 h-4 w-4\" />\n                    Home\n                  </Button>\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n        );\n      }\n\n      // Component-level error (inline)\n      return (\n        <div className=\"rounded-md border border-destructive/20 bg-destructive/5 p-4\">\n          <div className=\"flex items-start space-x-3\">\n            <AlertTriangle className=\"h-5 w-5 text-destructive mt-0.5\" />\n            <div className=\"flex-1 space-y-2\">\n              <h4 className=\"text-sm font-medium text-destructive\">\n                Component Error\n              </h4>\n              <p className=\"text-sm text-muted-foreground\">\n                This component failed to render properly.\n              </p>\n              {showDetails && error && (\n                <details className=\"text-xs\">\n                  <summary className=\"cursor-pointer text-muted-foreground hover:text-foreground\">\n                    Show details\n                  </summary>\n                  <pre className=\"mt-2 whitespace-pre-wrap font-mono text-xs bg-muted p-2 rounded\">\n                    {error.message}\n                  </pre>\n                </details>\n              )}\n              <Button size=\"sm\" variant=\"outline\" onClick={this.handleRetry}>\n                <RefreshCw className=\"mr-1 h-3 w-3\" />\n                Retry\n              </Button>\n            </div>\n          </div>\n        </div>\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\n// HOC for wrapping components with error boundary\nexport function withErrorBoundary<P extends object>(\n  Component: React.ComponentType<P>,\n  errorBoundaryProps?: Omit<Props, 'children'>\n) {\n  const WrappedComponent = (props: P) => (\n    <ErrorBoundary {...errorBoundaryProps}>\n      <Component {...props} />\n    </ErrorBoundary>\n  );\n\n  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`;\n  \n  return WrappedComponent;\n}\n\n// Hook for error boundary (for functional components)\nexport function useErrorHandler() {\n  return (error: Error, errorInfo?: ErrorInfo) => {\n    // This would typically integrate with your error reporting service\n    console.error('Error caught by useErrorHandler:', error, errorInfo);\n    \n    // You could also trigger a state update to show an error UI\n    throw error; // Re-throw to be caught by ErrorBoundary\n  };\n}\n\n// Simple error fallback components\nexport const SimpleErrorFallback: React.FC<{ error?: Error; retry?: () => void }> = ({ \n  error, \n  retry \n}) => (\n  <div className=\"text-center p-4\">\n    <AlertTriangle className=\"h-8 w-8 text-destructive mx-auto mb-2\" />\n    <h3 className=\"font-medium mb-1\">Something went wrong</h3>\n    <p className=\"text-sm text-muted-foreground mb-3\">\n      {error?.message || 'An unexpected error occurred'}\n    </p>\n    {retry && (\n      <Button size=\"sm\" onClick={retry}>\n        Try again\n      </Button>\n    )}\n  </div>\n);\n\nexport const MinimalErrorFallback: React.FC = () => (\n  <div className=\"flex items-center justify-center p-8 text-muted-foreground\">\n    <AlertTriangle className=\"h-4 w-4 mr-2\" />\n    <span className=\"text-sm\">Unable to load content</span>\n  </div>\n);\n\nexport default ErrorBoundary;\n"], "names": [], "mappings": ";;;;;;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AALA;;;;;;AAsBA,MAAM,sBAAsB,qMAAA,CAAA,YAAS;IACnC,YAAY,KAAY,CAAE;QACxB,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG;YACX,UAAU;YACV,OAAO;YACP,WAAW;YACX,SAAS;QACX;IACF;IAEA,OAAO,yBAAyB,KAAY,EAAkB;QAC5D,4DAA4D;QAC5D,OAAO;YACL,UAAU;YACV;YACA,SAAS,CAAC,MAAM,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG,IAAI;QAC3E;IACF;IAEA,kBAAkB,KAAY,EAAE,SAAoB,EAAE;QACpD,oBAAoB;QACpB,QAAQ,KAAK,CAAC,kCAAkC,OAAO;QAEvD,IAAI,CAAC,QAAQ,CAAC;YACZ;YACA;QACF;QAEA,wCAAwC;QACxC,IAAI,IAAI,CAAC,KAAK,CAAC,OAAO,EAAE;YACtB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,OAAO;QAC5B;QAEA,2EAA2E;QAC3E,uCAA2C;;QAE3C;IACF;IAEQ,oBAAoB,CAAC,OAAc;QACzC,sEAAsE;QACtE,MAAM,YAAY;YAChB,SAAS,MAAM,OAAO;YACtB,OAAO,MAAM,KAAK;YAClB,gBAAgB,UAAU,cAAc;YACxC,SAAS,IAAI,CAAC,KAAK,CAAC,OAAO;YAC3B,WAAW,IAAI,OAAO,WAAW;YACjC,WAAW,UAAU,SAAS;YAC9B,KAAK,OAAO,QAAQ,CAAC,IAAI;QAC3B;QAEA,2CAA2C;QAC3C,qDAAqD;QACrD,QAAQ,GAAG,CAAC,iBAAiB;IAC/B,EAAE;IAEM,cAAc;QACpB,IAAI,CAAC,QAAQ,CAAC;YACZ,UAAU;YACV,OAAO;YACP,WAAW;YACX,SAAS;QACX;IACF,EAAE;IAEM,eAAe;QACrB,OAAO,QAAQ,CAAC,MAAM;IACxB,EAAE;IAEM,eAAe;QACrB,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB,EAAE;IAEM,mBAAmB;QACzB,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK;QAChD,MAAM,eAAe;YACnB;YACA,SAAS,OAAO;YAChB,OAAO,OAAO;YACd,gBAAgB,WAAW;YAC3B,WAAW,IAAI,OAAO,WAAW;YACjC,KAAK,OAAO,QAAQ,CAAC,IAAI;QAC3B;QAEA,UAAU,SAAS,CAAC,SAAS,CAAC,KAAK,SAAS,CAAC,cAAc,MAAM;IACnE,EAAE;IAEF,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACvB,qBAAqB;YACrB,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;gBACvB,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;YAC5B;YAEA,MAAM,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK;YAChD,MAAM,EAAE,QAAQ,WAAW,EAAE,cAAc,KAAK,EAAE,GAAG,IAAI,CAAC,KAAK;YAE/D,6BAA6B;YAC7B,IAAI,UAAU,YAAY;gBACxB,qBACE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;;;;;;kDAE3B,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAW;;;;;;kDAChC,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;oCACpB,eAAe,uBACd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAAmB;;;;;;0DACjC,8OAAC;gDAAE,WAAU;0DACV,MAAM,OAAO;;;;;;4CAEf,yBACC,8OAAC;gDAAE,WAAU;;oDAAqC;oDACrC;;;;;;;;;;;;;kDAKnB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAS,IAAI,CAAC,YAAY;gDAAE,WAAU;;kEAC5C,8OAAC,gNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGxC,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,SAAS,IAAI,CAAC,YAAY;gDAAE,WAAU;;kEAC9D,8OAAC,mMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;4CAGlC,6BACC,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,SAAS,IAAI,CAAC,gBAAgB;;kEACtD,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAShD;YAEA,mBAAmB;YACnB,IAAI,UAAU,QAAQ;gBACpB,qBACE,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;4CAAC,WAAU;;;;;;;;;;;kDAE3B,8OAAC,gIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;oCACpB,eAAe,uBACd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DACV,MAAM,OAAO;;;;;;4CAEf,yBACC,8OAAC;gDAAE,WAAU;;oDAAqC;oDACrC;;;;;;;;;;;;;kDAKnB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAS,IAAI,CAAC,WAAW;gDAAE,WAAU;;kEAC3C,8OAAC,gNAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGxC,8OAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,SAAS,IAAI,CAAC,YAAY;;kEAClD,8OAAC,mMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQ/C;YAEA,iCAAiC;YACjC,qBACE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,wNAAA,CAAA,gBAAa;4BAAC,WAAU;;;;;;sCACzB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAuC;;;;;;8CAGrD,8OAAC;oCAAE,WAAU;8CAAgC;;;;;;gCAG5C,eAAe,uBACd,8OAAC;oCAAQ,WAAU;;sDACjB,8OAAC;4CAAQ,WAAU;sDAA6D;;;;;;sDAGhF,8OAAC;4CAAI,WAAU;sDACZ,MAAM,OAAO;;;;;;;;;;;;8CAIpB,8OAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAK,SAAQ;oCAAU,SAAS,IAAI,CAAC,WAAW;;sDAC3D,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;QAOlD;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;AACF;AAGO,SAAS,kBACd,SAAiC,EACjC,kBAA4C;IAE5C,MAAM,mBAAmB,CAAC,sBACxB,8OAAC;YAAe,GAAG,kBAAkB;sBACnC,cAAA,8OAAC;gBAAW,GAAG,KAAK;;;;;;;;;;;IAIxB,iBAAiB,WAAW,GAAG,CAAC,kBAAkB,EAAE,UAAU,WAAW,IAAI,UAAU,IAAI,CAAC,CAAC,CAAC;IAE9F,OAAO;AACT;AAGO,SAAS;IACd,OAAO,CAAC,OAAc;QACpB,mEAAmE;QACnE,QAAQ,KAAK,CAAC,oCAAoC,OAAO;QAEzD,4DAA4D;QAC5D,MAAM,OAAO,yCAAyC;IACxD;AACF;AAGO,MAAM,sBAAuE,CAAC,EACnF,KAAK,EACL,KAAK,EACN,iBACC,8OAAC;QAAI,WAAU;;0BACb,8OAAC,wNAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;0BACzB,8OAAC;gBAAG,WAAU;0BAAmB;;;;;;0BACjC,8OAAC;gBAAE,WAAU;0BACV,OAAO,WAAW;;;;;;YAEpB,uBACC,8OAAC,kIAAA,CAAA,SAAM;gBAAC,MAAK;gBAAK,SAAS;0BAAO;;;;;;;;;;;;AAOjC,MAAM,uBAAiC,kBAC5C,8OAAC;QAAI,WAAU;;0BACb,8OAAC,wNAAA,CAAA,gBAAa;gBAAC,WAAU;;;;;;0BACzB,8OAAC;gBAAK,WAAU;0BAAU;;;;;;;;;;;;uCAIf", "debugId": null}}, {"offset": {"line": 3978, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/test/dentist-appointment-platform/src/components/ui/sonner.tsx"], "sourcesContent": ["\"use client\"\n\nimport { useTheme } from \"next-themes\"\nimport { Toaster as Son<PERSON>, ToasterP<PERSON> } from \"sonner\"\n\nconst Toaster = ({ ...props }: ToasterProps) => {\n  const { theme = \"system\" } = useTheme()\n\n  return (\n    <Sonner\n      theme={theme as ToasterProps[\"theme\"]}\n      className=\"toaster group\"\n      style={\n        {\n          \"--normal-bg\": \"var(--popover)\",\n          \"--normal-text\": \"var(--popover-foreground)\",\n          \"--normal-border\": \"var(--border)\",\n        } as React.CSSProperties\n      }\n      {...props}\n    />\n  )\n}\n\nexport { Toaster }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,UAAU,CAAC,EAAE,GAAG,OAAqB;IACzC,MAAM,EAAE,QAAQ,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IAEpC,qBACE,8OAAC,wIAAA,CAAA,UAAM;QACL,OAAO;QACP,WAAU;QACV,OACE;YACE,eAAe;YACf,iBAAiB;YACjB,mBAAmB;QACrB;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 4012, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/test/dentist-appointment-platform/src/components/layout/AppLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { usePathname } from 'next/navigation';\nimport { cn } from '@/lib/utils';\nimport { useSidebar, usePageState } from '@/store/uiStore';\nimport { useAuth } from '@/store/authStore';\nimport { UserRole } from '@/types/auth';\nimport Sidebar from './Sidebar';\nimport Header from './Header';\nimport Breadcrumbs from './Breadcrumbs';\nimport ErrorBoundary from '@/components/common/ErrorBoundary';\nimport { Toaster } from '@/components/ui/sonner';\n\ninterface AppLayoutProps {\n  children: React.ReactNode;\n}\n\nconst AppLayout: React.FC<AppLayoutProps> = ({ children }) => {\n  const pathname = usePathname();\n  const { sidebarCollapsed, sidebarOpen, setSidebarOpen } = useSidebar();\n  const { pageTitle, breadcrumbs } = usePageState();\n  const { user, isAuthenticated } = useAuth();\n  const [isMobile, setIsMobile] = useState(false);\n\n  // Check if mobile\n  useEffect(() => {\n    const checkMobile = () => {\n      setIsMobile(window.innerWidth < 768);\n    };\n\n    checkMobile();\n    window.addEventListener('resize', checkMobile);\n    return () => window.removeEventListener('resize', checkMobile);\n  }, []);\n\n  // Close sidebar on mobile when route changes\n  useEffect(() => {\n    if (isMobile) {\n      setSidebarOpen(false);\n    }\n  }, [pathname, isMobile, setSidebarOpen]);\n\n  // Don't render layout for auth pages\n  const isAuthPage = pathname?.startsWith('/auth') || pathname === '/login' || pathname === '/register';\n  \n  if (isAuthPage || !isAuthenticated) {\n    return (\n      <ErrorBoundary level=\"critical\">\n        <div className=\"min-h-screen bg-background\">\n          {children}\n          <Toaster />\n        </div>\n      </ErrorBoundary>\n    );\n  }\n\n  return (\n    <ErrorBoundary level=\"critical\">\n      <div className=\"min-h-screen bg-background\">\n        {/* Sidebar */}\n        <Sidebar />\n\n        {/* Mobile sidebar overlay */}\n        {isMobile && sidebarOpen && (\n          <div\n            className=\"fixed inset-0 z-40 bg-background/80 backdrop-blur-sm md:hidden\"\n            onClick={() => setSidebarOpen(false)}\n          />\n        )}\n\n        {/* Main content */}\n        <div\n          className={cn(\n            'flex flex-col min-h-screen transition-all duration-300 ease-in-out',\n            isMobile ? 'ml-0' : sidebarCollapsed ? 'ml-20' : 'ml-72'\n          )}\n        >\n          {/* Header */}\n          <Header />\n\n          {/* Main content area */}\n          <main className=\"flex-1 flex flex-col\">\n            {/* Breadcrumbs */}\n            {breadcrumbs.length > 0 && (\n              <div className=\"border-b border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n                <div className=\"container mx-auto px-4 py-3\">\n                  <Breadcrumbs items={breadcrumbs} />\n                </div>\n              </div>\n            )}\n\n            {/* Page content */}\n            <div className=\"flex-1 container mx-auto px-4 py-6\">\n              <ErrorBoundary level=\"page\">\n                {children}\n              </ErrorBoundary>\n            </div>\n          </main>\n        </div>\n\n        {/* Toast notifications */}\n        <Toaster />\n      </div>\n    </ErrorBoundary>\n  );\n};\n\n// Layout wrapper for different user roles\ninterface RoleBasedLayoutProps {\n  children: React.ReactNode;\n  allowedRoles?: UserRole[];\n  fallback?: React.ReactNode;\n}\n\nexport const RoleBasedLayout: React.FC<RoleBasedLayoutProps> = ({\n  children,\n  allowedRoles = [],\n  fallback,\n}) => {\n  const { user } = useAuth();\n\n  if (allowedRoles.length > 0 && user && !allowedRoles.includes(user.role)) {\n    if (fallback) {\n      return <>{fallback}</>;\n    }\n\n    return (\n      <div className=\"flex min-h-[400px] items-center justify-center\">\n        <div className=\"text-center\">\n          <h2 className=\"text-2xl font-semibold mb-2\">Access Denied</h2>\n          <p className=\"text-muted-foreground\">\n            You don't have permission to access this page.\n          </p>\n        </div>\n      </div>\n    );\n  }\n\n  return <>{children}</>;\n};\n\n// Layout for patient dashboard\nexport const PatientLayout: React.FC<{ children: React.ReactNode }> = ({ children }) => (\n  <RoleBasedLayout allowedRoles={[UserRole.PATIENT]}>\n    <AppLayout>{children}</AppLayout>\n  </RoleBasedLayout>\n);\n\n// Layout for dentist dashboard\nexport const DentistLayout: React.FC<{ children: React.ReactNode }> = ({ children }) => (\n  <RoleBasedLayout allowedRoles={[UserRole.DENTIST, UserRole.ADMIN]}>\n    <AppLayout>{children}</AppLayout>\n  </RoleBasedLayout>\n);\n\n// Layout for admin dashboard\nexport const AdminLayout: React.FC<{ children: React.ReactNode }> = ({ children }) => (\n  <RoleBasedLayout allowedRoles={[UserRole.ADMIN]}>\n    <AppLayout>{children}</AppLayout>\n  </RoleBasedLayout>\n);\n\n// Simple layout without sidebar (for auth pages, etc.)\nexport const SimpleLayout: React.FC<{ children: React.ReactNode }> = ({ children }) => (\n  <ErrorBoundary level=\"critical\">\n    <div className=\"min-h-screen bg-background\">\n      {children}\n      <Toaster />\n    </div>\n  </ErrorBoundary>\n);\n\n// Full-width layout (for landing pages, etc.)\nexport const FullWidthLayout: React.FC<{ children: React.ReactNode }> = ({ children }) => (\n  <ErrorBoundary level=\"critical\">\n    <div className=\"min-h-screen bg-background\">\n      <main className=\"w-full\">\n        {children}\n      </main>\n      <Toaster />\n    </div>\n  </ErrorBoundary>\n);\n\n// Centered layout (for forms, modals, etc.)\nexport const CenteredLayout: React.FC<{ \n  children: React.ReactNode;\n  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl';\n}> = ({ children, maxWidth = 'md' }) => {\n  const maxWidthClasses = {\n    sm: 'max-w-sm',\n    md: 'max-w-md',\n    lg: 'max-w-lg',\n    xl: 'max-w-xl',\n    '2xl': 'max-w-2xl',\n  };\n\n  return (\n    <ErrorBoundary level=\"page\">\n      <div className=\"min-h-screen flex items-center justify-center p-4 bg-background\">\n        <div className={cn('w-full', maxWidthClasses[maxWidth])}>\n          {children}\n        </div>\n      </div>\n    </ErrorBoundary>\n  );\n};\n\nexport default AppLayout;\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA;;;;;;;;;;;;;AAkBA,MAAM,YAAsC,CAAC,EAAE,QAAQ,EAAE;IACvD,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,gBAAgB,EAAE,WAAW,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,aAAU,AAAD;IACnE,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,uHAAA,CAAA,eAAY,AAAD;IAC9C,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,UAAO,AAAD;IACxC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,kBAAkB;IAClB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,cAAc;YAClB,YAAY,OAAO,UAAU,GAAG;QAClC;QAEA;QACA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,6CAA6C;IAC7C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,UAAU;YACZ,eAAe;QACjB;IACF,GAAG;QAAC;QAAU;QAAU;KAAe;IAEvC,qCAAqC;IACrC,MAAM,aAAa,UAAU,WAAW,YAAY,aAAa,YAAY,aAAa;IAE1F,IAAI,cAAc,CAAC,iBAAiB;QAClC,qBACE,8OAAC,6IAAA,CAAA,UAAa;YAAC,OAAM;sBACnB,cAAA,8OAAC;gBAAI,WAAU;;oBACZ;kCACD,8OAAC,kIAAA,CAAA,UAAO;;;;;;;;;;;;;;;;IAIhB;IAEA,qBACE,8OAAC,6IAAA,CAAA,UAAa;QAAC,OAAM;kBACnB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC,uIAAA,CAAA,UAAO;;;;;gBAGP,YAAY,6BACX,8OAAC;oBACC,WAAU;oBACV,SAAS,IAAM,eAAe;;;;;;8BAKlC,8OAAC;oBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sEACA,WAAW,SAAS,mBAAmB,UAAU;;sCAInD,8OAAC,sIAAA,CAAA,UAAM;;;;;sCAGP,8OAAC;4BAAK,WAAU;;gCAEb,YAAY,MAAM,GAAG,mBACpB,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,2IAAA,CAAA,UAAW;4CAAC,OAAO;;;;;;;;;;;;;;;;8CAM1B,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,6IAAA,CAAA,UAAa;wCAAC,OAAM;kDAClB;;;;;;;;;;;;;;;;;;;;;;;8BAOT,8OAAC,kIAAA,CAAA,UAAO;;;;;;;;;;;;;;;;AAIhB;AASO,MAAM,kBAAkD,CAAC,EAC9D,QAAQ,EACR,eAAe,EAAE,EACjB,QAAQ,EACT;IACC,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,UAAO,AAAD;IAEvB,IAAI,aAAa,MAAM,GAAG,KAAK,QAAQ,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;QACxE,IAAI,UAAU;YACZ,qBAAO;0BAAG;;QACZ;QAEA,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA8B;;;;;;kCAC5C,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAM7C;IAEA,qBAAO;kBAAG;;AACZ;AAGO,MAAM,gBAAyD,CAAC,EAAE,QAAQ,EAAE,iBACjF,8OAAC;QAAgB,cAAc;YAAC,oHAAA,CAAA,WAAQ,CAAC,OAAO;SAAC;kBAC/C,cAAA,8OAAC;sBAAW;;;;;;;;;;;AAKT,MAAM,gBAAyD,CAAC,EAAE,QAAQ,EAAE,iBACjF,8OAAC;QAAgB,cAAc;YAAC,oHAAA,CAAA,WAAQ,CAAC,OAAO;YAAE,oHAAA,CAAA,WAAQ,CAAC,KAAK;SAAC;kBAC/D,cAAA,8OAAC;sBAAW;;;;;;;;;;;AAKT,MAAM,cAAuD,CAAC,EAAE,QAAQ,EAAE,iBAC/E,8OAAC;QAAgB,cAAc;YAAC,oHAAA,CAAA,WAAQ,CAAC,KAAK;SAAC;kBAC7C,cAAA,8OAAC;sBAAW;;;;;;;;;;;AAKT,MAAM,eAAwD,CAAC,EAAE,QAAQ,EAAE,iBAChF,8OAAC,6IAAA,CAAA,UAAa;QAAC,OAAM;kBACnB,cAAA,8OAAC;YAAI,WAAU;;gBACZ;8BACD,8OAAC,kIAAA,CAAA,UAAO;;;;;;;;;;;;;;;;AAMP,MAAM,kBAA2D,CAAC,EAAE,QAAQ,EAAE,iBACnF,8OAAC,6IAAA,CAAA,UAAa;QAAC,OAAM;kBACnB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAK,WAAU;8BACb;;;;;;8BAEH,8OAAC,kIAAA,CAAA,UAAO;;;;;;;;;;;;;;;;AAMP,MAAM,iBAGR,CAAC,EAAE,QAAQ,EAAE,WAAW,IAAI,EAAE;IACjC,MAAM,kBAAkB;QACtB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,OAAO;IACT;IAEA,qBACE,8OAAC,6IAAA,CAAA,UAAa;QAAC,OAAM;kBACnB,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,UAAU,eAAe,CAAC,SAAS;0BACnD;;;;;;;;;;;;;;;;AAKX;uCAEe", "debugId": null}}, {"offset": {"line": 4375, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/test/dentist-appointment-platform/src/components/providers/MockAuthProvider.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect } from 'react';\nimport { useAuthStore } from '@/store/authStore';\nimport { UserRole } from '@/types/auth';\n\ninterface MockAuthProviderProps {\n  children: React.ReactNode;\n}\n\nconst MockAuthProvider: React.FC<MockAuthProviderProps> = ({ children }) => {\n  const { setUser, setSession } = useAuthStore();\n\n  useEffect(() => {\n    // Set up a mock user for development\n    const mockUser = {\n      id: 'mock-user-1',\n      email: '<EMAIL>',\n      name: 'Dr. <PERSON>',\n      image: null,\n      role: UserRole.DENTIST,\n      createdAt: new Date(),\n      updatedAt: new Date(),\n      dentistProfile: {\n        id: 'dentist-profile-1',\n        userId: 'mock-user-1',\n        licenseNumber: 'DDS-12345',\n        specialization: ['General Dentistry', 'Cosmetic Dentistry'],\n        workingHours: {\n          monday: [{ start: '09:00', end: '17:00' }],\n          tuesday: [{ start: '09:00', end: '17:00' }],\n          wednesday: [{ start: '09:00', end: '17:00' }],\n          thursday: [{ start: '09:00', end: '17:00' }],\n          friday: [{ start: '09:00', end: '17:00' }],\n          saturday: [],\n          sunday: [],\n        },\n        practiceInfo: {\n          name: 'Smith Dental Practice',\n          address: '123 Main St, City, State 12345',\n          phone: '(*************',\n          email: '<EMAIL>',\n          website: 'https://smithdental.com',\n        },\n        createdAt: new Date(),\n        updatedAt: new Date(),\n        user: {} as any, // Will be filled by the relation\n      },\n    };\n\n    const mockSession = {\n      user: mockUser,\n      accessToken: 'mock-access-token',\n      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours from now\n    };\n\n    setUser(mockUser);\n    setSession(mockSession);\n  }, [setUser, setSession]);\n\n  return <>{children}</>;\n};\n\nexport default MockAuthProvider;\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAJA;;;;;AAUA,MAAM,mBAAoD,CAAC,EAAE,QAAQ,EAAE;IACrE,MAAM,EAAE,OAAO,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;IAE3C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,qCAAqC;QACrC,MAAM,WAAW;YACf,IAAI;YACJ,OAAO;YACP,MAAM;YACN,OAAO;YACP,MAAM,oHAAA,CAAA,WAAQ,CAAC,OAAO;YACtB,WAAW,IAAI;YACf,WAAW,IAAI;YACf,gBAAgB;gBACd,IAAI;gBACJ,QAAQ;gBACR,eAAe;gBACf,gBAAgB;oBAAC;oBAAqB;iBAAqB;gBAC3D,cAAc;oBACZ,QAAQ;wBAAC;4BAAE,OAAO;4BAAS,KAAK;wBAAQ;qBAAE;oBAC1C,SAAS;wBAAC;4BAAE,OAAO;4BAAS,KAAK;wBAAQ;qBAAE;oBAC3C,WAAW;wBAAC;4BAAE,OAAO;4BAAS,KAAK;wBAAQ;qBAAE;oBAC7C,UAAU;wBAAC;4BAAE,OAAO;4BAAS,KAAK;wBAAQ;qBAAE;oBAC5C,QAAQ;wBAAC;4BAAE,OAAO;4BAAS,KAAK;wBAAQ;qBAAE;oBAC1C,UAAU,EAAE;oBACZ,QAAQ,EAAE;gBACZ;gBACA,cAAc;oBACZ,MAAM;oBACN,SAAS;oBACT,OAAO;oBACP,OAAO;oBACP,SAAS;gBACX;gBACA,WAAW,IAAI;gBACf,WAAW,IAAI;gBACf,MAAM,CAAC;YACT;QACF;QAEA,MAAM,cAAc;YAClB,MAAM;YACN,aAAa;YACb,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK;QAClD;QAEA,QAAQ;QACR,WAAW;IACb,GAAG;QAAC;QAAS;KAAW;IAExB,qBAAO;kBAAG;;AACZ;uCAEe", "debugId": null}}]}